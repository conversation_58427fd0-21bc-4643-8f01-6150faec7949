{"name": "netconfig-web", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "serve": "vite preview"}, "devDependencies": {"@stagewise-plugins/vue": "^0.4.7", "@stagewise/toolbar": "^0.4.9", "@stagewise/toolbar-vue": "^0.4.9", "@vitejs/plugin-vue": "^4.3.0", "@vue/compiler-sfc": "^3.3.4", "postcss-import": "^16.0.0", "postcss-nested": "^6.0.1", "postcss-nesting": "^12.0.2", "vite": "^4.4.9"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@mdi/font": "^7.4.47", "autoprefixer": "^10.4.21", "axios": "^1.6.2", "dompurify": "^3.2.6", "echarts": "^5.4.3", "element-plus": "^2.4.3", "express": "^4.18.2", "jspdf": "^2.5.1", "marked": "^16.1.1", "pinia": "^2.1.7", "postcss": "^8.5.6", "sortablejs": "^1.15.6", "tailwindcss": "^3.3.3", "vis-data": "^7.1.7", "vis-network": "^9.1.6", "vue": "^3.3.4", "vue-i18n": "^9.8.0", "vue-router": "^4.2.5", "vuetify": "^3.4.7", "xlsx-js-style": "^1.2.0", "xterm": "^5.3.0", "xterm-addon-attach": "^0.9.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-search": "^0.13.0", "xterm-addon-web-links": "^0.9.0", "xterm-addon-webgl": "^0.16.0"}, "main": "vite.config.js", "keywords": [], "author": "", "license": "ISC", "description": ""}