{"name": "xlsx-js-style", "version": "1.2.0", "author": "git<PERSON>nt", "description": "SheetJS Spreadsheet Community Edition plus Basic Cell Styles", "homepage": "https://github.com/gitbrent/xlsx-js-style/", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "office", "spreadsheet", "js-xlsx", "xlsx-js", "sheetjs-style", "sheetjs-style-v2"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "dist/xlsx.min.js", "unpkg": "dist/xlsx.min.js", "jsdelivr": "dist/xlsx.min.js", "files": ["dist", "libs", "types"], "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.4", "codepage": "~1.14.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "fflate": "^0.3.8", "ssf": "~0.11.2", "wmf": "~1.0.1", "word": "~0.3.0"}, "devDependencies": {"@types/node": "^8.5.9", "gulp": "^4.0.2", "gulp-concat": "^2.6.1", "gulp-ignore": "^3.0.0", "gulp-insert": "^0.5.0", "gulp-replace": "^1.1.3", "gulp-sourcemaps": "^3.0.0", "gulp-uglify": "^3.0.2"}, "repository": {"type": "git", "url": "git://github.com/gitbrent/xlsx-js-style.git"}, "scripts": {"ship": "gulp ship"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "bugs": {"url": "https://github.com/gitbrent/xlsx-js-style/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}}