<template>
  <div class="topology-container">
    <!-- 左侧设备库 -->
    <div class="sidebar">
      <div class="sidebar-header">设备库</div>
      <div class="search-box">
        <input type="text" placeholder="搜索设备..." class="search-input" />
      </div>
      <div class="tab-container">
                  <div class="tabs">
            <div class="tabs-row">
              <div class="tab" :class="{ active: activeTab === 'router' }" @click="setActiveTab('router')">路由</div>
              <div class="tab" :class="{ active: activeTab === 'switch' }" @click="setActiveTab('switch')">交换</div>
              <div class="tab" :class="{ active: activeTab === 'server' }" @click="setActiveTab('server')">服务器</div>
              <div class="tab" :class="{ active: activeTab === 'firewall' }" @click="setActiveTab('firewall')">防火墙</div>
            </div>
            <div class="tabs-row">
              <div class="tab" :class="{ active: activeTab === 'wireless' }" @click="setActiveTab('wireless')">无线</div>
              <div class="tab" :class="{ active: activeTab === 'cloud' }" @click="setActiveTab('cloud')">云</div>
              <div class="tab" :class="{ active: activeTab === 'client' }" @click="setActiveTab('client')">终端</div>
            </div>
          </div>
        <div class="device-list">
          <div class="device-item" 
            v-for="device in devices" 
            :key="device.id"
            draggable="true"
            @dragstart="onDeviceDragStart($event, device)">
            <div class="device-icon">
              <svg viewBox="0 0 24 24" class="device-svg modern-icon">
                <path v-if="device.iconType === 'mdi'" :d="device.icon" fill="var(--primary-color)" />
                <path v-else :d="device.icon" fill="var(--primary-color)" />
              </svg>
            </div>
            <div class="device-info">
              <div class="device-name">{{ device.name }}</div>
              <div class="device-type">{{ device.type }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <div class="top-toolbar">
        <div class="toolbar-group">
          <div class="toolbar-item" :class="{ active: currentTool === 'select' }" title="选择工具" @click="selectTool('select')">
            <svg viewBox="0 0 24 24" width="18" height="18" class="modern-icon">
              <path :d="mdIcons.cursorDefault" fill="currentColor" />
            </svg>
          </div>
          <div class="toolbar-item" :class="{ active: currentTool === 'move' }" title="移动" @click="selectTool('move')">
            <svg viewBox="0 0 24 24" width="18" height="18" class="modern-icon">
              <path :d="mdIcons.arrowAll" fill="currentColor" />
            </svg>
          </div>
          <div class="toolbar-item" :class="{ active: currentTool === 'connect' }" title="连线" @click="selectTool('connect')">
            <svg viewBox="0 0 24 24" width="18" height="18" class="modern-icon">
              <path :d="mdIcons.vectorLine" fill="currentColor" />
            </svg>
          </div>
          <div class="toolbar-item" :class="{ active: currentTool === 'edit' }" title="编辑" @click="selectTool('edit')">
            <svg viewBox="0 0 24 24" width="18" height="18" class="modern-icon">
              <path :d="mdIcons.pencil" fill="currentColor" />
            </svg>
          </div>
          <div class="toolbar-item" :class="{ active: currentTool === 'delete' }" title="删除" @click="selectTool('delete')">
            <svg viewBox="0 0 24 24" width="18" height="18" class="modern-icon">
              <path :d="mdIcons.delete" fill="currentColor" />
            </svg>
          </div>
          <div class="toolbar-item" title="调色板" @click="openPalette">
            <svg viewBox="0 0 24 24" width="18" height="18" class="modern-icon">
              <path :d="mdIcons.palette" fill="currentColor" />
            </svg>
          </div>
        </div>
        
        <div class="toolbar-divider-vertical"></div>
        
        <div class="toolbar-group">
          <div class="toolbar-item" title="缩小" @click="zoomOut">
            <svg viewBox="0 0 24 24" width="18" height="18" class="modern-icon">
              <path :d="mdIcons.minus" fill="currentColor" />
            </svg>
          </div>
          <div class="toolbar-item" title="重置视图" @click="resetZoom">
            <svg viewBox="0 0 24 24" width="18" height="18" class="modern-icon">
              <path :d="mdIcons.eye" fill="currentColor" />
            </svg>
          </div>
          <div class="toolbar-item" title="放大" @click="zoomIn">
            <svg viewBox="0 0 24 24" width="18" height="18" class="modern-icon">
              <path :d="mdIcons.plus" fill="currentColor" />
            </svg>
          </div>
        </div>
        
        <div class="toolbar-divider-vertical"></div>
        
        <div class="toolbar-group">
          <div class="toolbar-item" title="新建" @click="createNewTopology">
            <svg viewBox="0 0 24 24" width="18" height="18" class="modern-icon">
              <path :d="mdIcons.plus" fill="currentColor" />
            </svg>
          </div>
          <div class="toolbar-item" title="保存" @click="saveTopology">
            <svg viewBox="0 0 24 24" width="18" height="18" class="modern-icon">
              <path :d="mdIcons.save" fill="currentColor" />
            </svg>
          </div>
          <div class="toolbar-item" title="打开" @click="uploadTopology">
            <svg viewBox="0 0 24 24" width="18" height="18" class="modern-icon">
              <path :d="mdIcons.upload" fill="currentColor" />
            </svg>
          </div>
          <div class="toolbar-item" title="导出连线表" @click="exportConnectionTable">
            <svg viewBox="0 0 24 24" width="18" height="18" class="modern-icon">
              <path :d="mdIcons.fileDocument" fill="currentColor" />
            </svg>
          </div>
          <div class="toolbar-item" :class="{ active: showConnectionList }" title="连线列表" @click="showConnectionList = !showConnectionList">
            <svg viewBox="0 0 24 24" width="18" height="18" class="modern-icon">
              <path :d="mdIcons.viewList" fill="currentColor" />
            </svg>
          </div>
          <div class="toolbar-item" :class="{ disabled: historyIndex <= 0 }" title="撤销" @click="historyIndex > 0 && undo()">
            <svg viewBox="0 0 24 24" width="18" height="18" class="modern-icon">
              <path :d="mdIcons.undo" fill="currentColor" />
            </svg>
          </div>
          <div class="toolbar-item" :class="{ disabled: historyIndex >= history.length - 1 }" title="重做" @click="historyIndex < history.length - 1 && redo()">
            <svg viewBox="0 0 24 24" width="18" height="18" class="modern-icon">
              <path :d="mdIcons.redo" fill="currentColor" />
            </svg>
          </div>
        </div>
      </div>

      <!-- 中间拓扑图区域 -->
      <div class="topology-canvas">
        <div class="canvas-header">
          <div class="header-title">网络拓扑图</div>
          <div class="header-subtitle" v-if="unsavedChanges">
            <span class="unsaved-indicator"></span>未保存的更改
          </div>
          <div class="header-subtitle" v-else>{{ topologyFileName || '新拓扑' }}</div>
        </div>
        
        <div class="canvas-content"
           :class="{ 'current-tool-connect': currentTool === 'connect', 'grid-enabled': gridEnabled, 'drawing-mode': isDrawingMode }"
           :style="[
             gridEnabled ? { backgroundSize: gridSize + 'px ' + gridSize + 'px' } : {},
             { cursor: isDrawingMode ? 'crosshair' : 'default' }
           ]"
           @dragover.prevent
           @drop="onDeviceDrop($event)"
           @mousedown="isDrawingMode ? startDrawing($event) : onCanvasMouseDown($event)"
           @mousemove="isDrawingMode ? onDrawingMove($event) : onCanvasMouseMove($event)"
           @mouseup="isDrawingMode ? finishDrawing($event) : onCanvasMouseUp($event)"
           @click="onCanvasClick($event)"
           @dblclick="onCanvasDblClick($event)">
          <div class="empty-canvas" v-if="canvasDevices.length === 0">
            <div class="empty-icon">
              <svg viewBox="0 0 24 24" width="48" height="48">
                <path d="M19,3H5C3.9,3,3,3.9,3,5v14c0,1.1,0.9,2,2,2h14c1.1,0,2-0.9,2-2v-9 C22,8.9,21.1,8,20,8z M19,19H5V5h14V19z M13.96,12.29 l-2.75,3.54l-1.96-2.36L6.5,17h11L13.96,12.29z" fill="#ccc" />
              </svg>
            </div>
            <div class="empty-text">开始构建您的网络拓扑</div>
            <div class="empty-subtext">从左侧设备库拖拽设备到画面上</div>
          </div>
          
          <!-- 连线层 -->
          <svg class="connections-layer" width="100%" height="100%" style="position: absolute; top: 0; left: 0;">
            <!-- 辅助线 -->
            <g class="alignment-guides" v-if="alignmentGuides.enabled && alignmentGuides.showGuides">
              <!-- 水平辅助线 -->
              <line 
                v-for="(guide, index) in alignmentGuides.horizontalGuides" 
                :key="'h-' + index"
                :x1="0" 
                :y1="guide" 
                :x2="10000" 
                :y2="guide"
                class="alignment-guide horizontal-guide" 
              />
              
              <!-- 垂直辅助线 -->
              <line 
                v-for="(guide, index) in alignmentGuides.verticalGuides" 
                :key="'v-' + index"
                :x1="guide" 
                :y1="0" 
                :x2="guide" 
                :y2="10000"
                class="alignment-guide vertical-guide" 
              />
            </g>
            
            <!-- 现有连线 -->
            <g v-for="connection in connections" :key="connection.id">
              <path 
                :d="getConnectionPath(connection)" 
                :class="{ 'connection-path': true, 'selected': connection === selectedItem }"
                @mousedown.stop="selectConnection(connection, $event)"
                :data-id="connection.id"
                :style="{ 
                  'pointer-events': 'all',
                  'stroke': connection.color || '#1890ff',
                  'stroke-width': (connection.strokeWidth || 2) + 'px',
                  'stroke-dasharray': connection.strokeStyle === 'dashed' ? '5, 3' : 
                                      connection.strokeStyle === 'dotted' ? '2, 2' : 'none'
                }"
              />
              
              <!-- 源端口标签 -->
              <g v-if="connection.sourcePort" 
                 @mousedown.stop="selectAndDragLabel($event, connection, 'source')"
                 style="pointer-events: all; cursor: move;">
                <rect 
                  :x="getSourcePortLabelPosition(connection).x - 25" 
                  :y="getSourcePortLabelPosition(connection).y - 10"
                  :width="connection.sourcePort.length * 7 + 10" height="20" rx="5" ry="5"
                  class="port-label-box"
                />
                <text 
                  :x="getSourcePortLabelPosition(connection).x" 
                  :y="getSourcePortLabelPosition(connection).y"
                  class="port-label"
                  text-anchor="middle"
                  dominant-baseline="middle"
                >
                  {{ connection.sourcePort }}
                </text>
              </g>
              
              <!-- 目标端口标签 -->
              <g v-if="connection.targetPort"
                 @mousedown.stop="selectAndDragLabel($event, connection, 'target')"
                 style="pointer-events: all; cursor: move;">
                <rect 
                  :x="getTargetPortLabelPosition(connection).x - 25" 
                  :y="getTargetPortLabelPosition(connection).y - 10"
                  :width="connection.targetPort.length * 7 + 10" height="20" rx="5" ry="5"
                  class="port-label-box"
                />
                <text 
                  :x="getTargetPortLabelPosition(connection).x" 
                  :y="getTargetPortLabelPosition(connection).y"
                  class="port-label"
                  text-anchor="middle"
                  dominant-baseline="middle"
                >
                  {{ connection.targetPort }}
                </text>
              </g>
              
              <!-- 连线标签 - 只在需要时显示 -->
              <g v-if="connection.label"
                 @mousedown.stop="selectAndDragLabel($event, connection)"
                 style="pointer-events: all; cursor: move;">
                <rect 
                  :x="getConnectionLabelPosition(connection).x - 40" 
                  :y="getConnectionLabelPosition(connection).y - 10"
                  width="80" height="20" rx="5" ry="5"
                  class="port-label-box"
                />
                <text 
                  :x="getConnectionLabelPosition(connection).x" 
                  :y="getConnectionLabelPosition(connection).y"
                  class="connection-label"
                  text-anchor="middle"
                  dominant-baseline="middle"
                  style="pointer-events: all;"
                >
                  {{ connection.label }}
                </text>
              </g>
            </g>
            
            <!-- 正在创建的连线 -->
            <path 
              v-if="isCreatingConnection" 
              :d="getNewConnectionPath()" 
              class="connection-path new-connection"
            />
            
            <!-- 已保存的形状 -->
            <g>
              <!-- 矩形 -->
              <rect 
                v-for="shape in shapes.filter(s => s.type === 'rectangle')"
                :key="shape.id"
                :x="Math.min(shape.x1, shape.x2)" 
                :y="Math.min(shape.y1, shape.y2)"
                :width="Math.abs(shape.x2 - shape.x1)" 
                :height="Math.abs(shape.y2 - shape.y1)"
                :fill="shape.isFilled ? shape.color : 'none'"
                :fill-opacity="shape.isFilled ? 0.3 : 0"
                :stroke="shape.color"
                :stroke-width="shape.strokeWidth"
                :stroke-dasharray="shape.strokeStyle === 'dashed' ? '5, 3' : 
                                  shape.strokeStyle === 'dotted' ? '2, 2' : 'none'"
                @click.stop="selectShape(shape, $event)"
                @dblclick.stop.prevent="setShapeEditMode(shape, $event)"
                :class="{ 'shape-element': true, 'selected': shape === selectedItem, 'shape-edit-mode': shape === editingShape }"
              />
              
              <!-- 圆形 -->
              <ellipse 
                v-for="shape in shapes.filter(s => s.type === 'circle')"
                :key="shape.id"
                :cx="(shape.x1 + shape.x2) / 2" 
                :cy="(shape.y1 + shape.y2) / 2"
                :rx="Math.abs(shape.x2 - shape.x1) / 2" 
                :ry="Math.abs(shape.y2 - shape.y1) / 2"
                :fill="shape.isFilled ? shape.color : 'none'"
                :fill-opacity="shape.isFilled ? 0.3 : 0"
                :stroke="shape.color"
                :stroke-width="shape.strokeWidth"
                :stroke-dasharray="shape.strokeStyle === 'dashed' ? '5, 3' : 
                                  shape.strokeStyle === 'dotted' ? '2, 2' : 'none'"
                @click.stop="selectShape(shape, $event)"
                @dblclick.stop.prevent="setShapeEditMode(shape, $event)"
                :class="{ 'shape-element': true, 'selected': shape === selectedItem, 'shape-edit-mode': shape === editingShape }"
              />
              
              <!-- 线条 -->
              <line 
                v-for="shape in shapes.filter(s => s.type === 'line')"
                :key="shape.id"
                :x1="shape.x1" 
                :y1="shape.y1"
                :x2="shape.x2" 
                :y2="shape.y2"
                :stroke="shape.color"
                :stroke-width="shape.strokeWidth"
                :stroke-dasharray="shape.strokeStyle === 'dashed' ? '5, 3' : 
                                  shape.strokeStyle === 'dotted' ? '2, 2' : 'none'"
                @click.stop="selectShape(shape, $event)"
                @dblclick.stop.prevent="setShapeEditMode(shape, $event)"
                :class="{ 'shape-element': true, 'selected': shape === selectedItem, 'shape-edit-mode': shape === editingShape }"
              />
              
              <!-- 编辑模式下的控制点 -->
              <g v-if="editingShape">
                <!-- 矩形或圆形的控制点 -->
                <g v-if="editingShape.type === 'rectangle' || editingShape.type === 'circle'">
                  <!-- 顶点控制点 -->
                  <circle
                    class="control-point corner-point"
                    :cx="Math.min(editingShape.x1, editingShape.x2)"
                    :cy="Math.min(editingShape.y1, editingShape.y2)"
                    r="6"
                    @mousedown.stop="startControlPointDrag($event, 'tl')"
                  />
                  <circle
                    class="control-point corner-point"
                    :cx="Math.max(editingShape.x1, editingShape.x2)"
                    :cy="Math.min(editingShape.y1, editingShape.y2)"
                    r="6"
                    @mousedown.stop="startControlPointDrag($event, 'tr')"
                  />
                  <circle
                    class="control-point corner-point"
                    :cx="Math.min(editingShape.x1, editingShape.x2)"
                    :cy="Math.max(editingShape.y1, editingShape.y2)"
                    r="6"
                    @mousedown.stop="startControlPointDrag($event, 'bl')"
                  />
                  <circle
                    class="control-point corner-point"
                    :cx="Math.max(editingShape.x1, editingShape.x2)"
                    :cy="Math.max(editingShape.y1, editingShape.y2)"
                    r="6"
                    @mousedown.stop="startControlPointDrag($event, 'br')"
                  />
                  
                  <!-- 中心控制点 -->
                  <circle
                    class="control-point midpoint"
                    :cx="(editingShape.x1 + editingShape.x2) / 2"
                    :cy="(editingShape.y1 + editingShape.y2) / 2"
                    r="6"
                    @mousedown.stop="startShapeDrag($event)"
                  />
                </g>
                
                <!-- 线条的控制点 -->
                <g v-if="editingShape.type === 'line'">
                  <circle
                    class="control-point endpoint"
                    :cx="editingShape.x1"
                    :cy="editingShape.y1"
                    r="6"
                    @mousedown.stop="startControlPointDrag($event, 'start')"
                  />
                  <circle
                    class="control-point endpoint"
                    :cx="editingShape.x2"
                    :cy="editingShape.y2"
                    r="6"
                    @mousedown.stop="startControlPointDrag($event, 'end')"
                  />
                  
                  <!-- 中点拖动控制 -->
                  <circle
                    class="control-point midpoint"
                    :cx="(editingShape.x1 + editingShape.x2) / 2"
                    :cy="(editingShape.y1 + editingShape.y2) / 2"
                    r="6"
                    @mousedown.stop="startShapeDrag($event)"
                  />
                </g>
              </g>
            </g>
            
            <!-- 正在绘制的形状 -->
            <g v-if="isDrawingMode && drawStartX && drawCurrentX">
              <!-- 矩形 -->
              <rect 
                v-if="drawingShape === 'rectangle'" 
                :x="Math.min(drawStartX, drawCurrentX)" 
                :y="Math.min(drawStartY, drawCurrentY)"
                :width="Math.abs(drawCurrentX - drawStartX)" 
                :height="Math.abs(drawCurrentY - drawStartY)"
                :fill="isFilled ? currentColor : 'none'"
                :fill-opacity="isFilled ? 0.3 : 0"
                :stroke="currentColor"
                :stroke-width="borderWidth"
                :stroke-dasharray="selectedBorderStyle === 'dashed' ? '5, 3' : 
                                  selectedBorderStyle === 'dotted' ? '2, 2' : 'none'"
              />
              
              <!-- 圆形 -->
              <ellipse 
                v-if="drawingShape === 'circle'" 
                :cx="(drawStartX + drawCurrentX) / 2" 
                :cy="(drawStartY + drawCurrentY) / 2"
                :rx="Math.abs(drawCurrentX - drawStartX) / 2" 
                :ry="Math.abs(drawCurrentY - drawStartY) / 2"
                :fill="isFilled ? currentColor : 'none'"
                :fill-opacity="isFilled ? 0.3 : 0"
                :stroke="currentColor"
                :stroke-width="borderWidth"
                :stroke-dasharray="selectedBorderStyle === 'dashed' ? '5, 3' : 
                                  selectedBorderStyle === 'dotted' ? '2, 2' : 'none'"
              />
              
              <!-- 线条 -->
              <line 
                v-if="drawingShape === 'line'" 
                :x1="drawStartX" 
                :y1="drawStartY"
                :x2="drawCurrentX" 
                :y2="drawCurrentY"
                :stroke="currentColor"
                :stroke-width="borderWidth"
                :stroke-dasharray="selectedBorderStyle === 'dashed' ? '5, 3' : 
                                  selectedBorderStyle === 'dotted' ? '2, 2' : 'none'"
              />
            </g>
          </svg>
          
          <!-- 放置在画布上的设备 -->
          <div class="canvas-device" 
               v-for="device in canvasDevices" 
               :key="device.id"
               :style="{ left: device.x + 'px', top: device.y + 'px' }"
               :class="{ 'selected': device === selectedItem }"
               @mousedown="onDeviceMouseDown($event, device)">
            <div class="canvas-device-icon">
              <svg viewBox="0 0 24 24" width="36" height="36" class="modern-icon">
                <path v-if="device.iconType === 'mdi'" :d="device.icon" fill="var(--primary-color)" />
                <path v-else :d="device.icon" fill="var(--primary-color)" />
              </svg>
            </div>
            <div class="canvas-device-label" 
                 :style="{ 
                   whiteSpace: 'pre-line',
                   transform: `translate(-50%, 0) translate(${device.labelOffsetX || 0}px, ${device.labelOffsetY || 0}px)`
                 }"
                 @mousedown.stop="startLabelDrag($event, device, 'device')">
              {{ device.name }}
              <template v-if="device.type && showDeviceType">
                <br>{{ device.type }}
              </template>
              <template v-if="device.ipAddress && showDeviceIP">
                <br>{{ device.ipAddress }}
              </template>
              <template v-if="device.vendor && showDeviceVendor">
                <br>{{ device.vendor }}
              </template>
            </div>
          </div>
        </div>
        
        <div class="canvas-footer">
          <div class="footer-info">设备数量: {{ canvasDevices.length }}</div>
          <div class="footer-info">连接数量: {{ connections.length }}</div>
          <div class="footer-info">当前工具: {{ currentTool === 'select' ? '选择' : 
                                         currentTool === 'move' ? '移动' : 
                                         currentTool === 'connect' ? '连线' : 
                                         currentTool === 'edit' ? '编辑' : 
                                         currentTool === 'delete' ? '删除' : '选择' }}</div>
          <div class="canvas-controls">
            <span>缩放: {{ zoom }}%</span>
            <span>网格: {{ gridEnabled ? '开启' : '关闭' }}</span>
          </div>
        </div>
      </div>
      
      <!-- 添加连线列表视图 -->
      <div class="connection-list-panel" v-if="showConnectionList" :style="{ height: connectionListHeight + 'px' }">
        <div class="panel-resizer" @mousedown="startResizingPanel"></div>
        <div class="panel-header">
          <div class="panel-title">连线列表</div>
          <div class="panel-actions">
            <button class="panel-close-btn" @click="showConnectionList = false">×</button>
          </div>
        </div>
        
        <div class="panel-filters">
          <div class="filter-group">
            <input 
              type="text" 
              v-model="connectionFilter.search" 
              placeholder="搜索设备或端口..." 
              class="filter-input"
              @input="filterConnections" 
            />
          </div>
          
          <div class="filter-group">
            <select v-model="connectionFilter.sortBy" class="filter-select" @change="filterConnections">
              <option value="source">按源设备排序</option>
              <option value="target">按目标设备排序</option>
              <option value="sourcePort">按源端口排序</option>
              <option value="targetPort">按目标端口排序</option>
            </select>
            
            <button 
              class="sort-direction-btn" 
              @click="toggleSortDirection" 
              :title="connectionFilter.sortAsc ? '升序' : '降序'"
            >
              <svg viewBox="0 0 24 24" width="16" height="16">
                <path :d="connectionFilter.sortAsc ? 
                  'M7,10L12,15L17,10H7Z' : 
                  'M7,15L12,10L17,15H7Z'" 
                fill="currentColor" />
              </svg>
            </button>
          </div>
        </div>
        
        <div class="connection-table-container">
          <table class="connection-table">
            <thead>
              <tr>
                <th @click="setSortBy('source')" :class="{ active: connectionFilter.sortBy === 'source' }">
                  本端设备
                  <span class="sort-icon" v-if="connectionFilter.sortBy === 'source'">
                    {{ connectionFilter.sortAsc ? '↓' : '↑' }}
                  </span>
                </th>
                <th @click="setSortBy('sourcePort')" :class="{ active: connectionFilter.sortBy === 'sourcePort' }">
                  本端端口
                  <span class="sort-icon" v-if="connectionFilter.sortBy === 'sourcePort'">
                    {{ connectionFilter.sortAsc ? '↓' : '↑' }}
                  </span>
                </th>
                <th @click="setSortBy('target')" :class="{ active: connectionFilter.sortBy === 'target' }">
                  对端设备
                  <span class="sort-icon" v-if="connectionFilter.sortBy === 'target'">
                    {{ connectionFilter.sortAsc ? '↓' : '↑' }}
                  </span>
                </th>
                <th @click="setSortBy('targetPort')" :class="{ active: connectionFilter.sortBy === 'targetPort' }">
                  对端端口
                  <span class="sort-icon" v-if="connectionFilter.sortBy === 'targetPort'">
                    {{ connectionFilter.sortAsc ? '↓' : '↑' }}
                  </span>
                </th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="connection in filteredConnections" :key="connection.id" 
                  :class="{ selected: connection === selectedItem }"
                  @click="selectConnectionFromList(connection)">
                <td>{{ getDeviceName(connection.source) }}</td>
                <td>{{ connection.sourcePort || '未指定' }}</td>
                <td>{{ getDeviceName(connection.target) }}</td>
                <td>{{ connection.targetPort || '未指定' }}</td>
                <td>
                  <button class="action-btn locate-btn" @click.stop="locateConnection(connection)" title="定位到图形">
                    <svg viewBox="0 0 24 24" width="14" height="14">
                      <path d="M12,9A3,3 0 0,1 15,12A3,3 0 0,1 12,15A3,3 0 0,1 9,12A3,3 0 0,1 12,9M12,4.5C17,4.5 21.27,7.61 23,12C21.27,16.39 17,19.5 12,19.5C7,19.5 2.73,16.39 1,12C2.73,7.61 7,4.5 12,4.5Z" fill="currentColor" />
                    </svg>
                  </button>
                  <button class="action-btn delete-btn" @click.stop="deleteConnection(connection)" title="删除连线">
                    <svg viewBox="0 0 24 24" width="14" height="14">
                      <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z" fill="currentColor" />
                    </svg>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
          
          <div class="connection-count">
            显示 {{ filteredConnections.length }} / {{ connections.length }} 条连线
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧属性面板 -->
    <div class="properties-panel">
      <div class="panel-header">属性面板</div>
      <div class="panel-content">
        <!-- 未选中任何项时显示的内容 -->
        <div class="panel-empty" v-if="!selectedItem">
          <div class="empty-icon">
            <svg viewBox="0 0 24 24" width="32" height="32">
              <path d="M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,17H13V11H11V17Z" fill="#999" />
            </svg>
          </div>
          <div class="empty-text">选择设备或连线查看属性</div>
        </div>
        
        <!-- 图形属性 -->
        <div class="property-section" v-if="selectedItem && selectedItem.type && !selectedItem.source">
          <div class="section-title">图形属性</div>
          
          <div class="property-item">
            <div class="property-label">颜色</div>
            <div class="property-value">
              <input type="color" v-model="selectedItem.color" @change="updateShape" class="property-input color-picker" />
            </div>
          </div>
          
          <div class="property-item">
            <div class="property-label">线条粗细</div>
            <div class="property-value">
              <select v-model="selectedItem.strokeWidth" @change="updateShape" class="property-select">
                <option v-for="n in 10" :key="n" :value="n">{{ n }}px</option>
              </select>
            </div>
          </div>
          
          <div class="property-item">
            <div class="property-label">线条样式</div>
            <div class="property-value">
              <select v-model="selectedItem.strokeStyle" @change="updateShape" class="property-select">
                <option value="solid">实线</option>
                <option value="dashed">虚线</option>
                <option value="dotted">点线</option>
              </select>
            </div>
          </div>
          
          <div class="property-item" v-if="selectedItem.type !== 'line'">
            <div class="property-label">填充</div>
            <div class="property-value">
              <label class="switch">
                <input type="checkbox" v-model="selectedItem.isFilled" @change="updateShape">
                <span class="slider"></span>
              </label>
            </div>
          </div>
        </div>
        
        <!-- 设备属性 -->
        <div class="property-section" v-if="selectedItem && !selectedItem.source && !selectedItem.type">
          <div class="section-title">设备属性</div>
          
          <div class="property-item">
            <div class="property-label">设备名称</div>
            <div class="property-value">
              <input type="text" v-model="selectedItem.name" class="property-input" @change="updateDevice">
            </div>
          </div>
          
          <div class="property-item">
            <div class="property-label">设备类型</div>
            <div class="property-value">
              <input type="text" v-model="selectedItem.type" class="property-input" @change="updateDevice">
            </div>
          </div>
          
          <div class="property-item">
            <div class="property-label">厂商</div>
            <div class="property-value">
              <input type="text" v-model="selectedItem.vendor" class="property-input" @change="updateDevice">
            </div>
          </div>
          
          <div class="property-item">
            <div class="property-label">IP地址</div>
            <div class="property-value">
              <input type="text" v-model="selectedItem.ipAddress" class="property-input" @change="updateDevice">
            </div>
          </div>
          
          <div class="property-item">
            <div class="property-label">型号</div>
            <div class="property-value">
              <input type="text" v-model="selectedItem.model" class="property-input" @change="updateDevice">
            </div>
          </div>
          
          <div class="property-item">
            <div class="property-label">位置</div>
            <div class="property-value">
              <input type="text" v-model="selectedItem.location" class="property-input" @change="updateDevice">
            </div>
          </div>
          
          <div class="property-item">
            <div class="property-label">描述</div>
            <div class="property-value" style="width: 100%;">
              <textarea v-model="selectedItem.description" class="property-textarea" @change="updateDevice" style="width: 100%; height: 60px;"></textarea>
            </div>
          </div>
          
          <div class="property-item">
            <div class="property-label">位置 X</div>
            <div class="property-value">
              <input type="number" v-model.number="selectedItem.x" class="property-input" @change="updateDevice">
            </div>
          </div>
          
          <div class="property-item">
            <div class="property-label">位置 Y</div>
            <div class="property-value">
              <input type="number" v-model.number="selectedItem.y" class="property-input" @change="updateDevice">
            </div>
          </div>
        </div>
        
        <!-- 连线属性 -->
        <div class="property-section" v-if="selectedItem && selectedItem.source">
          <div class="section-title">连线设置</div>
          
          <div class="property-item">
            <div class="property-label">连线标签</div>
            <div class="property-value">
              <input type="text" v-model="selectedItem.label" class="property-input" @change="updateConnection">
            </div>
          </div>
          
          <div class="property-item">
            <div class="property-label">源端口</div>
            <div class="property-value">
              <input type="text" v-model="selectedItem.sourcePort" class="property-input" @change="updateConnection">
            </div>
          </div>
          
          <div class="property-item">
            <div class="property-label">目标端口</div>
            <div class="property-value">
              <input type="text" v-model="selectedItem.targetPort" class="property-input" @change="updateConnection">
            </div>
          </div>
          
          <div class="property-item">
            <div class="property-label">路径样式</div>
            <div class="property-value">
              <select class="property-select" v-model="selectedItem.type" @change="updateConnection">
                <option v-for="type in connectionTypes" :key="type" :value="type">{{ type }}</option>
              </select>
            </div>
          </div>
          
          <div class="property-item">
            <div class="property-label">智能路由</div>
            <div class="property-value">
              <label class="switch">
                <input type="checkbox" v-model="smartRouting" @change="updateConnection">
                <span class="slider"></span>
              </label>
            </div>
          </div>
          
          <div class="property-item">
            <div class="property-label">避免重叠</div>
            <div class="property-value">
              <label class="switch">
                <input type="checkbox" v-model="avoidOverlap" @change="updateConnection">
                <span class="slider"></span>
              </label>
            </div>
          </div>
          
          <div class="property-item">
            <div class="property-label">删除连线</div>
            <div class="property-value">
              <button class="delete-btn" @click="deleteConnection(selectedItem)">删除</button>
            </div>
          </div>
        </div>
        
        <!-- 画布属性 -->
        <div class="property-section" v-if="!selectedItem">
          <div class="section-title">画布设置</div>
          
          <div class="property-item">
            <div class="property-label">默认连线样式</div>
            <div class="property-value">
              <select class="property-select" v-model="pathStyle">
                <option v-for="type in connectionTypes" :key="type" :value="type">{{ type }}</option>
              </select>
            </div>
          </div>
          
          <div class="property-item">
            <div class="property-label">网格</div>
            <div class="property-value">
              <label class="switch">
                <input type="checkbox" v-model="gridEnabled">
                <span class="slider"></span>
              </label>
            </div>
          </div>
          
          <div class="property-item">
            <div class="property-label">网格大小: {{ gridSize }}px</div>
            <div class="property-value">
              <input type="range" min="5" max="50" v-model.number="gridSize" class="slider" style="width: 100%; max-width: 120px;">
            </div>
          </div>
          
          <div class="property-item">
            <div class="property-label">辅助对齐</div>
            <div class="property-value">
              <label class="switch">
                <input type="checkbox" v-model="alignmentGuides.enabled">
                <span class="slider"></span>
              </label>
            </div>
          </div>
          
          <div class="property-item" v-if="alignmentGuides.enabled">
            <div class="property-label">对齐阈值: {{ alignmentGuides.threshold }}px</div>
            <div class="property-value">
              <input type="range" min="2" max="20" v-model.number="alignmentGuides.threshold" class="slider" style="width: 100%; max-width: 120px;">
            </div>
          </div>
          
          <div class="section-title" style="margin-top: 20px;">设备标签设置</div>
          
          <div class="property-item">
            <div class="property-label">显示设备类型</div>
            <div class="property-value">
              <label class="switch">
                <input type="checkbox" v-model="showDeviceType">
                <span class="slider"></span>
              </label>
            </div>
          </div>
          
          <div class="property-item">
            <div class="property-label">显示IP地址</div>
            <div class="property-value">
              <label class="switch">
                <input type="checkbox" v-model="showDeviceIP">
                <span class="slider"></span>
              </label>
            </div>
          </div>
          
          <div class="property-item">
            <div class="property-label">显示厂商</div>
            <div class="property-value">
              <label class="switch">
                <input type="checkbox" v-model="showDeviceVendor">
                <span class="slider"></span>
              </label>
            </div>
          </div>
          
          <div class="property-item">
            <div class="property-label">标签提示</div>
            <div class="property-value" style="color: #1890ff;">
              可拖动设备标签调整位置
            </div>
          </div>
          
          <!-- 添加连线列表按钮到画布属性中 -->
          <div class="property-item">
            <div class="property-label">连线列表</div>
            <div class="property-value">
              <button class="action-button" @click="showConnectionList = !showConnectionList">
                {{ showConnectionList ? '隐藏列表' : '显示列表' }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 端口选择对话框 -->
    <div class="port-dialog" v-if="showPortDialog">
      <div class="port-dialog-content">
        <div class="port-dialog-header">
          <h3>请选择端口</h3>
          <div class="dialog-device-info">设备: {{ portDialogDevice ? portDialogDevice.name : '' }}</div>
        </div>
        <div class="port-dialog-body">
          <!-- 端口组合生成器 -->
          <div class="port-generator">
            <div class="port-generator-header">端口组合生成器</div>
            <div class="port-generator-content">
              <div class="port-generator-row">
                <div class="port-generator-label">接口类型:</div>
                <select v-model="portGenerator.type" class="port-generator-select">
                  <option value="GE">GigabitEthernet (GE)</option>
                  <option value="XGE">TenGigabitEthernet (XGE)</option>
                  <option value="FE">FastEthernet (FE)</option>
                  <option value="Eth">Ethernet (Eth)</option>
                  <option value="Serial">Serial</option>
                  <option value="VLAN">VLAN</option>
                  <option value="Lo">Loopback (Lo)</option>
                </select>
              </div>
              
              <div class="port-generator-row">
                <div class="port-generator-label">层级数量:</div>
                <select v-model="portGenerator.levels" class="port-generator-select">
                  <option value="1">1位 (例如: GE1)</option>
                  <option value="2">2位 (例如: GE0/1)</option>
                  <option value="3">3位 (例如: GE1/0/1)</option>
                  <option value="4">4位 (例如: GE1/1/0/1)</option>
                </select>
              </div>
              
              <div class="port-generator-row" v-if="portGenerator.levels >= 1">
                <div class="port-generator-label">第1位:</div>
                <select v-model="portGenerator.level1" class="port-generator-select">
                  <option v-for="n in 10" :key="n-1" :value="n-1">{{ n-1 }}</option>
                </select>
              </div>
              
              <div class="port-generator-row" v-if="portGenerator.levels >= 2">
                <div class="port-generator-label">第2位:</div>
                <select v-model="portGenerator.level2" class="port-generator-select">
                  <option v-for="n in 10" :key="n-1" :value="n-1">{{ n-1 }}</option>
                </select>
              </div>
              
              <div class="port-generator-row" v-if="portGenerator.levels >= 3">
                <div class="port-generator-label">第3位:</div>
                <select v-model="portGenerator.level3" class="port-generator-select">
                  <option v-for="n in 50" :key="n-1" :value="n-1">{{ n-1 }}</option>
                </select>
              </div>
              
              <div class="port-generator-row" v-if="portGenerator.levels >= 4">
                <div class="port-generator-label">第4位:</div>
                <select v-model="portGenerator.level4" class="port-generator-select">
                  <option v-for="n in 10" :key="n-1" :value="n-1">{{ n-1 }}</option>
                </select>
              </div>
              
              <div class="port-generator-result">
                <div class="port-generator-label">生成的端口号:</div>
                <div class="port-generator-value">{{ generatedPortId }}</div>
                <button class="port-generator-use-btn" @click="useGeneratedPort">使用</button>
              </div>
            </div>
          </div>
          
          <div class="port-input-container">
            <label for="port-number">自定义端口号:</label>
            <input 
              type="text" 
              id="port-number" 
              v-model="selectedPort" 
              class="port-input" 
              placeholder="例如: GE1/0/1"
              ref="portInput"
              @keyup.enter="confirmPortSelection"
            />
          </div>
        </div>
        <div class="port-dialog-footer">
          <button class="cancel-btn" @click="cancelPortSelection">取消</button>
          <button class="confirm-btn" @click="confirmPortSelection">确定</button>
        </div>
      </div>
    </div>
    
    <!-- 保存拓扑图对话框 -->
    <div class="dialog-overlay" v-if="showSaveDialog">
      <div class="dialog-content">
        <div class="dialog-header">
          <h3>保存拓扑图</h3>
        </div>
        <div class="dialog-body">
          <div class="form-item">
            <label for="topology-name">拓扑图名称:</label>
            <input 
              type="text" 
              id="topology-name" 
              v-model="topologyFileName" 
              ref="saveNameInput"
              class="form-input" 
              @keyup.enter="confirmSaveTopology"
            />
          </div>
        </div>
        <div class="dialog-footer">
          <button class="cancel-btn" @click="showSaveDialog = false">取消</button>
          <button class="confirm-btn" @click="confirmSaveTopology">保存</button>
          <button class="export-btn" @click="exportTopologyAsJson">导出为JSON</button>
        </div>
      </div>
    </div>
    
    <!-- 提示消息 -->
    <div class="toast-message" v-if="showToastMessage">
      {{ toastMessage }}
    </div>
    
    <!-- 确认对话框 -->
    <div class="dialog-overlay" v-if="showConfirmDialog">
      <div class="dialog-content confirm-dialog">
        <div class="dialog-header">
          <h3>确认操作</h3>
        </div>
        <div class="dialog-body">
          <p>{{ confirmDialogMessage }}</p>
        </div>
        <div class="dialog-footer">
          <button class="cancel-btn" @click="cancelConfirm">取消</button>
          <button class="confirm-btn" @click="confirmAction">确定</button>
        </div>
      </div>
    </div>
    
    <!-- 调色板对话框 -->
    <div class="dialog-overlay" v-if="showPaletteDialog">
      <div class="dialog-content palette-dialog">
        <div class="dialog-header">
          <h3>调色板</h3>
          <button class="close-btn" @click="showPaletteDialog = false">×</button>
        </div>
        <div class="dialog-body">
          <!-- 边框类型选择 -->
          <div class="palette-row">
            <div 
              v-for="type in borderTypes" 
              :key="type.id" 
              class="border-type-option" 
              :class="{ active: selectedBorderType === type.id }"
              @click="selectedBorderType = type.id"
            >
              <div class="border-type-icon">
                <div v-if="type.id === 'line'" class="line-icon"></div>
                <div v-else-if="type.id === 'rectangle'" class="rectangle-icon"></div>
                <div v-else-if="type.id === 'circle'" class="circle-icon"></div>
              </div>
            </div>
          </div>
          
          <!-- 边框样式和粗细选择 -->
          <div class="palette-row">
            <div class="palette-label">边框类型：</div>
            <div class="palette-input">
              <select v-model="selectedBorderStyle" class="style-select">
                <option v-for="style in borderStyles" :key="style.id" :value="style.id">
                  {{ style.name }}
                </option>
              </select>
            </div>
          </div>
          
          <div class="palette-row">
            <div class="palette-label">边框粗细：</div>
            <div class="palette-input">
              <select v-model.number="borderWidth" class="style-select">
                <option v-for="n in 5" :key="n" :value="n">{{ n }}</option>
              </select>
            </div>
          </div>
          
          <!-- 颜色选择和填充选项 -->
          <div class="palette-row">
            <div class="color-preview" :style="{ backgroundColor: currentColor }" @click="openColorPicker"></div>
            <input type="color" ref="colorInput" v-model="currentColor" style="display:none" @change="updateColor">
            
            <div class="fill-options">
              <label class="fill-option">
                <input type="radio" v-model="isFilled" :value="false"> 不填充
              </label>
              <label class="fill-option">
                <input type="radio" v-model="isFilled" :value="true"> 填充
              </label>
            </div>
          </div>
        </div>
        <div class="dialog-footer">
          <button class="cancel-btn" @click="cancelPaletteSettings">取消</button>
          <button class="confirm-btn" @click="applyPaletteSettings">确定</button>
        </div>
      </div>
    </div>
    
    <!-- 打开拓扑图对话框 -->
    <div class="dialog-overlay" v-if="showOpenDialog">
      <div class="dialog-content">
        <div class="dialog-header">
          <h3>打开拓扑图</h3>
        </div>
        <div class="dialog-body">
          <!-- 保存的拓扑图列表 -->
          <div class="saved-topologies-list" v-if="savedTopologies.length > 0">
            <div class="saved-topology-item" v-for="topology in savedTopologies" :key="topology.name">
              <div class="saved-topology-info">
                <div class="saved-topology-name">{{ topology.name }}</div>
                <div class="saved-topology-date">{{ new Date(topology.date).toLocaleString() }}</div>
              </div>
              <div class="saved-topology-actions">
                <button class="open-btn" @click="loadSavedTopology(topology)">打开</button>
                <button class="delete-btn" @click="deleteSavedTopology(topology.name)">删除</button>
              </div>
            </div>
          </div>
          
          <!-- 没有保存的拓扑图时显示 -->
          <div class="no-saved-topologies" v-else>
            <p>没有找到保存的拓扑图</p>
          </div>
          
          <!-- 导入JSON文件选项 -->
          <div class="import-option">
            <button class="import-btn" @click="importTopologyFromJson">从JSON文件导入</button>
          </div>
        </div>
        <div class="dialog-footer">
          <button class="cancel-btn" @click="showOpenDialog = false">取消</button>
        </div>
      </div>
    </div>
    
    <!-- 导出连线表对话框 -->
    <div class="dialog-overlay" v-if="showExportDialog">
      <div class="dialog-content">
        <div class="dialog-header">
          <h3>导出连线表</h3>
          <button class="close-btn" @click="showExportDialog = false">×</button>
        </div>
        <div class="dialog-body">
          <div class="export-options">
            <div class="export-option">
              <input type="radio" id="export-csv" v-model="exportFormat" value="csv">
              <label for="export-csv">CSV格式 (.csv)</label>
            </div>
            <div class="export-option">
              <input type="radio" id="export-excel" v-model="exportFormat" value="excel">
              <label for="export-excel">Excel格式 (.xlsx)</label>
            </div>
          </div>
          
          <div class="connection-preview" v-if="connections.length > 0">
            <h4>连线预览 (显示前5条)</h4>
            <table class="connection-table">
              <thead>
                <tr>
                  <th>本端设备</th>
                  <th>本端端口</th>
                  <th>对端设备</th>
                  <th>对端端口</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(connection, index) in connections.slice(0, 5)" :key="connection.id">
                  <td>{{ getDeviceName(connection.source) }}</td>
                  <td>{{ connection.sourcePort || '未指定' }}</td>
                  <td>{{ getDeviceName(connection.target) }}</td>
                  <td>{{ connection.targetPort || '未指定' }}</td>
                </tr>
              </tbody>
            </table>
            <div class="connection-count" v-if="connections.length > 5">
              共 {{ connections.length }} 条连线
            </div>
          </div>
          <div class="no-connections" v-else>
            <p>当前拓扑图没有连线数据</p>
          </div>
        </div>
        <div class="dialog-footer">
          <button class="cancel-btn" @click="showExportDialog = false">取消</button>
          <button class="confirm-btn" @click="confirmExport" :disabled="connections.length === 0">导出</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent } from 'vue';
// 导入Material Design Icons图标
import { 
  mdiCursorDefault, mdiArrowAll, mdiVectorLine, mdiPencil, mdiDelete, 
  mdiPalette, mdiMinus, mdiEye, mdiPlus, mdiContentSave, mdiCloudUpload, 
  mdiFileDocument, mdiViewList, mdiUndo, mdiRedo, mdiShield, 
  mdiAccessPoint, mdiNetworkOutline, mdiServer, mdiDesktopClassic, 
  mdiRouter, mdiSwitch, mdiLan, mdiViewDashboardOutline, 
  mdiCloudOutline, mdiWifiStrength4, mdiLaptop,
  mdiDesktopTowerMonitor, mdiCellphoneLink, mdiTablet, mdiPrinter,
  mdiDatabaseOutline, mdiFileMultipleOutline, mdiServerNetwork
} from '@mdi/js';

export default {
  name: 'TopologyView',
  data() {
    return {
      // Material Design Icons图标路径
      mdIcons: {
        cursorDefault: mdiCursorDefault,
        arrowAll: mdiArrowAll,
        vectorLine: mdiVectorLine,
        pencil: mdiPencil,
        delete: mdiDelete,
        palette: mdiPalette,
        minus: mdiMinus,
        eye: mdiEye,
        plus: mdiPlus,
        save: mdiContentSave,
        upload: mdiCloudUpload,
        fileDocument: mdiFileDocument,
        viewList: mdiViewList,
        undo: mdiUndo,
        redo: mdiRedo,
        shield: mdiShield,
        accessPoint: mdiAccessPoint,
        network: mdiNetworkOutline,
        server: mdiServer,
        desktop: mdiDesktopClassic,
        router: mdiRouter,
        switch: mdiSwitch,
        lan: mdiLan,
        dashboard: mdiViewDashboardOutline
      },
      devices: [],
      connections: [],
      selectedItem: null,
      zoom: 100,
      gridEnabled: true,
      gridSize: 20,
      pathStyle: '直线',
      smartRouting: true,
      avoidOverlap: true,
      currentTool: 'select',
      activeTab: 'router',
      firewallDevices: [
        { 
          id: 'ngfw',
          name: '下一代防火墙',
          type: 'ngfw',
          iconType: 'mdi',
          icon: mdiShield
        },
        { 
          id: 'waf',
          name: 'Web应用防火墙',
          type: 'waf',
          iconType: 'mdi',
          icon: mdiShield
        },
        { 
          id: 'utm',
          name: '统一威胁管理',
          type: 'utm',
          iconType: 'mdi',
          icon: mdiShield
        }
      ],
      wirelessDevices: [
        { 
          id: 'ap',
          name: '无线接入点',
          type: 'ap',
          iconType: 'mdi',
          icon: mdiWifiStrength4
        },
        { 
          id: 'wlan-controller',
          name: '无线控制器',
          type: 'wlan-controller',
          iconType: 'mdi',
          icon: mdiAccessPoint
        },
        { 
          id: 'mesh-ap',
          name: 'Mesh无线',
          type: 'mesh-ap',
          iconType: 'mdi',
          icon: mdiWifiStrength4
        }
      ],
      cloudDevices: [
        { 
          id: 'cloud-server',
          name: '云服务器',
          type: 'cloud-server',
          iconType: 'mdi',
          icon: mdiCloudOutline
        },
        { 
          id: 'cloud-storage',
          name: '云存储',
          type: 'cloud-storage',
          iconType: 'mdi',
          icon: mdiCloudOutline
        },
        { 
          id: 'cloud-gateway',
          name: '云网关',
          type: 'cloud-gateway',
          iconType: 'mdi',
          icon: mdiNetworkOutline
        }
      ],
      clientDevices: [
        { 
          id: 'pc',
          name: 'PC电脑',
          type: 'pc',
          iconType: 'mdi',
          icon: mdiDesktopTowerMonitor
        },
        { 
          id: 'laptop',
          name: '笔记本电脑',
          type: 'laptop',
          iconType: 'mdi',
          icon: mdiLaptop
        },
        { 
          id: 'smartphone',
          name: '智能手机',
          type: 'smartphone',
          iconType: 'mdi',
          icon: mdiCellphoneLink
        },
        { 
          id: 'tablet',
          name: '平板电脑',
          type: 'tablet',
          iconType: 'mdi',
          icon: mdiTablet
        },
        { 
          id: 'printer',
          name: '打印机',
          type: 'printer',
          iconType: 'mdi',
          icon: mdiPrinter
        }
      ],
      // 辅助线相关
      alignmentGuides: {
        enabled: true,        // 是否启用辅助线
        threshold: 10,        // 吸附阈值（像素）
        showGuides: false,    // 是否显示辅助线
        horizontalGuides: [], // 水平辅助线位置
        verticalGuides: [],   // 垂直辅助线位置
      },
      // 添加历史记录相关数据
      history: [],
      historyIndex: -1,
      maxHistoryLength: 50,
      // 添加文件相关数据
      topologyFile: null,
      topologyFileName: '',
      unsavedChanges: false,
      // 添加对话框相关数据
      showSaveDialog: false,
      showOpenDialog: false,
      showConfirmDialog: false,
      showPaletteDialog: false,
      showExportDialog: false, // 导出连线表对话框
      confirmDialogAction: null, // 用于存储确认后要执行的动作
      confirmDialogMessage: '', // 确认对话框的消息
      savedTopologies: [],
      // 添加提示消息相关数据
      showToastMessage: false,
      toastMessage: '',
      // 调色板相关数据
      currentColor: '#0078FF', // 默认蓝色
      borderTypes: [
        { id: 'line', name: '线条' },
        { id: 'rectangle', name: '矩形' },
        { id: 'circle', name: '圆形' }
      ],
      selectedBorderType: 'line',
      borderStyles: [
        { id: 'solid', name: 'Solid' },
        { id: 'dashed', name: 'Dashed' },
        { id: 'dotted', name: 'Dotted' }
      ],
      selectedBorderStyle: 'solid',
      borderWidth: 1,
      isFilled: true,
      // 绘图模式相关数据
      isDrawingMode: false,
      drawingShape: null,
      drawStartX: 0,
      drawStartY: 0,
      drawCurrentX: 0,
      drawCurrentY: 0,
      // 绘制的形状
      shapes: [],
      // 添加图形编辑相关数据
      editingShape: null,           // 当前正在编辑的图形
      isShapeDragging: false,       // 是否正在拖动图形
      isControlPointDragging: false, // 是否正在拖动控制点
      draggedControlPoint: null,    // 当前拖动的控制点类型
      shapeDragStartX: 0,           // 图形拖动起始X坐标
      shapeDragStartY: 0,           // 图形拖动起始Y坐标
      shapeDragOffsetX: 0,          // 图形拖动X偏移
      shapeDragOffsetY: 0,          // 图形拖动Y偏移
      originalShapeData: null,      // 原始图形数据，用于拖动或缩放
      routerDevices: [
        { 
          id: 'core-router',
          name: '核心路由器',
          type: 'router',
          iconType: 'mdi',
          icon: mdiRouter
        },
        { 
          id: 'edge-router',
          name: '边缘路由器',
          type: 'edge-router',
          iconType: 'mdi',
          icon: mdiRouter
        },
        { 
          id: 'vpn-router',
          name: 'VPN路由器',
          type: 'vpn-router',
          iconType: 'mdi',
          icon: mdiShield
        },
        { 
          id: 'wireless-router',
          name: '无线路由器',
          type: 'wireless-router',
          iconType: 'mdi',
          icon: mdiWifiStrength4
        },
        { 
          id: 'enterprise-router',
          name: '企业路由器',
          type: 'enterprise-router',
          icon: 'M2,2V4H22V2H2M20,8H18V6H16V8H8V6H6V8H4V20H20V8M9,10H10V11H9V10M9,12H10V13H9V12M9,14H10V15H9V14M12,10H13V11H12V10M12,12H13V13H12V12M12,14H13V15H12V14M15,10H16V11H15V10M15,12H16V13H15V12M15,14H16V15H15V14Z'
        }
      ],
      switchDevices: [
        { 
          id: 'core-switch',
          name: '核心交换机',
          type: 'switch',
          iconType: 'mdi',
          icon: mdiSwitch
        },
        { 
          id: 'aggregation-switch',
          name: '汇聚交换机',
          type: 'aggregation-switch',
          iconType: 'mdi',
          icon: mdiSwitch
        },
        { 
          id: 'access-switch',
          name: '接入交换机',
          type: 'access-switch',
          iconType: 'mdi',
          icon: mdiSwitch
        },
        { 
          id: 'l3-switch',
          name: '三层交换机',
          type: 'l3-switch',
          iconType: 'mdi',
          icon: mdiServerNetwork
        },
        { 
          id: 'poe-switch',
          name: 'PoE交换机',
          type: 'poe-switch',
          iconType: 'mdi',
          icon: mdiSwitch
        }
      ],
      serverDevices: [
        { 
          id: 'web-server',
          name: 'Web服务器',
          type: 'server',
          iconType: 'mdi',
          icon: mdiServer
        },
        { 
          id: 'db-server',
          name: '数据库服务器',
          type: 'db-server',
          iconType: 'mdi',
          icon: mdiDatabaseOutline
        },
        { 
          id: 'file-server',
          name: '文件服务器',
          type: 'file-server',
          iconType: 'mdi',
          icon: mdiFileMultipleOutline
        },
        { 
          id: 'app-server',
          name: '应用服务器',
          type: 'app-server',
          iconType: 'mdi',
          icon: mdiServer
        },
        { 
          id: 'vm-server',
          name: '虚拟化服务器',
          type: 'vm-server',
          iconType: 'mdi',
          icon: mdiServerNetwork
        }
      ],
      canvasDevices: [],
      draggedDevice: null,
      isDragging: false,
      dragOffsetX: 0,
      dragOffsetY: 0,
      isCreatingConnection: false,
      newConnectionStart: null,
      newConnectionEnd: null,
      connectionTypes: ['正交线', '直线', '曲线'],
      showPortDialog: false,
      portDialogDevice: null,
      selectedPort: '',
      isFirstDeviceSelected: false,
      firstDeviceData: null,
      connectionInProgress: false,
      isDraggingLabel: false,
      draggedLabel: null,
      dragLabelOffsetX: 0,
      dragLabelOffsetY: 0,
      draggedLabelType: null,
      portGenerator: {
        type: 'GE',
        levels: 3,
        level1: 1,
        level2: 0,
        level3: 1,
        level4: 0
      },
      exportFormat: 'csv',
      
      // 连线列表相关数据
      showConnectionList: false,
      connectionFilter: {
        search: '',
        sortBy: 'source',
        sortAsc: true
      },
      filteredConnections: [],
      connectionListHeight: 280,
      isResizingPanel: false,
      panelResizeStartY: 0,
      panelResizeStartHeight: 0,
    }
  },
  computed: {
    // 根据当前活动标签返回对应的设备列表
    activeDevices() {
      switch(this.activeTab) {
        case 'router':
          return this.routerDevices;
        case 'switch':
          return this.switchDevices;
        case 'server':
          return this.serverDevices;
        case 'firewall':
          return this.firewallDevices;
        case 'wireless':
          return this.wirelessDevices;
        case 'cloud':
          return this.cloudDevices;
        case 'client':
          return this.clientDevices;
        default:
          return this.routerDevices;
      }
    },
    
    // 计算生成的端口号
    generatedPortId() {
      const { type, levels, level1, level2, level3, level4 } = this.portGenerator;
      let portId = type;
      
      switch (parseInt(levels)) {
        case 1:
          portId += level1;
          break;
        case 2:
          portId += level1 + '/' + level2;
          break;
        case 3:
          portId += level1 + '/' + level2 + '/' + level3;
          break;
        case 4:
          portId += level1 + '/' + level2 + '/' + level3 + '/' + level4;
          break;
      }
      
      return portId;
    }
  },
  methods: {
    // 选择工具
    selectTool(toolName) {
      console.log('选择工具:', toolName);
      this.currentTool = toolName;
      
      // 退出绘图模式（如果有）
      if (this.isDrawingMode) {
        this.isDrawingMode = false;
        this.drawingShape = null;
        document.body.style.cursor = 'default';
      }
      
      // 如果选择了连接工具，重置连接状态
      if (toolName === 'connect') {
        this.isCreatingConnection = false;
        this.newConnectionStart = null;
        this.newConnectionEnd = null;
        console.log('已切换到连接工具，连接点应该可见');
      }
    },
    
    // 缩放操作
    zoomIn() {
      this.zoom = Math.min(this.zoom + 10, 200);
      this.updateCanvasZoom();
    },
    
    zoomOut() {
      this.zoom = Math.max(this.zoom - 10, 50);
      this.updateCanvasZoom();
    },
    
    resetZoom() {
      this.zoom = 100;
      this.updateCanvasZoom();
    },
    
    updateCanvasZoom() {
      // 更新画布缩放比例
      const canvasContent = document.querySelector('.canvas-content');
      if (canvasContent) {
        canvasContent.style.transform = `scale(${this.zoom / 100})`;
        canvasContent.style.transformOrigin = 'center center';
      }
      console.log(`画布缩放至 ${this.zoom}%`);
    },

    setActiveTab(tab) {
      this.activeTab = tab;
      this.devices = this.activeDevices; // 更新显示的设备列表
    },
 
    onDeviceDragStart(event, device) {
      // 设置拖拽数据
      this.draggedDevice = device;
      event.dataTransfer.setData('text/plain', device.id);
      // 可以设置拖动时的效果
      event.dataTransfer.effectAllowed = 'copy';
    },
 
    onDeviceDrop(event) {
      event.preventDefault();
      
      if (this.draggedDevice) {
        // 获取放置的位置（相对于画布）
        const canvasRect = event.currentTarget.getBoundingClientRect();
        const x = event.clientX - canvasRect.left;
        const y = event.clientY - canvasRect.top;
        
        // 创建一个新设备到画布
        const newDevice = {
          ...this.draggedDevice,
          id: `${this.draggedDevice.id}-${Date.now()}`, // 确保ID唯一
          x: x - 25, // 调整以使图标中心在鼠标位置
          y: y - 25,
          labelOffsetX: 0, // 初始化标签偏移
          labelOffsetY: 0  // 默认位置在设备下方，通过CSS控制
        };
        
        // 添加历史记录
        this.addToHistory();
        
        this.canvasDevices.push(newDevice);
        this.draggedDevice = null;
      }
    },
 
    startDrag(event, device) {
      // 只有在选择工具激活时才启用拖动
      if (this.currentTool !== 'select' && this.currentTool !== 'move') {
        return;
      }
      
      this.isDragging = true;
      this.selectedItem = device;
      
      // 计算鼠标点击位置与设备左上角的偏移
      const deviceElement = event.currentTarget;
      const deviceRect = deviceElement.getBoundingClientRect();
      
      this.dragOffsetX = event.clientX - deviceRect.left;
      this.dragOffsetY = event.clientY - deviceRect.top;
      
      // 添加鼠标移动和释放事件监听器
      window.addEventListener('mousemove', this.onDrag);
      window.addEventListener('mouseup', this.stopDrag);
    },
    
    onDrag(event) {
      if (!this.isDragging || !this.selectedItem) return;
      
      // 获取画布位置
      const canvas = document.querySelector('.canvas-content');
      const canvasRect = canvas.getBoundingClientRect();
      
      // 计算新位置
      let newX = event.clientX - canvasRect.left - this.dragOffsetX;
      let newY = event.clientY - canvasRect.top - this.dragOffsetY;
      
      // 确保设备不会超出画布边界
      newX = Math.max(0, Math.min(newX, canvasRect.width - 50));
      newY = Math.max(0, Math.min(newY, canvasRect.height - 50));
      
      // 如果启用了网格功能，可以将设备吸附到网格
      if (this.gridEnabled) {
        newX = Math.round(newX / this.gridSize) * this.gridSize;
        newY = Math.round(newY / this.gridSize) * this.gridSize;
      }
      
      // 更新设备位置
      const deviceIndex = this.canvasDevices.findIndex(d => d.id === this.selectedItem.id);
      if (deviceIndex !== -1) {
        const deviceId = this.selectedItem.id;
        const oldX = this.canvasDevices[deviceIndex].x;
        const oldY = this.canvasDevices[deviceIndex].y;
        
        // 如果启用了辅助线，计算对齐位置
        if (this.alignmentGuides.enabled) {
          const result = this.calculateAlignmentGuides(newX, newY, deviceIndex);
          newX = result.x;
          newY = result.y;
          
          // 更新辅助线显示状态
          this.alignmentGuides.showGuides = result.hasGuides;
          this.alignmentGuides.horizontalGuides = result.horizontalGuides;
          this.alignmentGuides.verticalGuides = result.verticalGuides;
        }
        
        // 更新设备位置
        this.canvasDevices[deviceIndex].x = newX;
        this.canvasDevices[deviceIndex].y = newY;
        
        // 更新与该设备相关的所有连接
        // 首先找出所有与该设备相关的连接
        const relatedConnections = this.connections.filter(conn => 
          conn.source === deviceId || conn.target === deviceId
        );
        
        // 对于每个目标设备，获取所有连接
        const targetDeviceMap = new Map();
        
        relatedConnections.forEach(connection => {
          const isSource = connection.source === deviceId;
          const otherDeviceId = isSource ? connection.target : connection.source;
          
          if (!targetDeviceMap.has(otherDeviceId)) {
            targetDeviceMap.set(otherDeviceId, []);
          }
          
          targetDeviceMap.get(otherDeviceId).push(connection);
        });
        
        // 现在对每个目标设备的连接进行处理
        targetDeviceMap.forEach((connections, otherDeviceId) => {
          // 按照端口号排序
          connections.sort((a, b) => {
            const aPort = a.source === deviceId ? a.sourcePort : a.targetPort;
            const bPort = b.source === deviceId ? b.sourcePort : b.targetPort;
            return aPort.localeCompare(bPort);
          });
          
          // 获取目标设备
          const otherDevice = this.canvasDevices.find(d => d.id === otherDeviceId);
          
          // 更新每个连接的坐标
          connections.forEach((connection, idx) => {
            const isSource = connection.source === deviceId;
            
            if (isSource) {
              // 当前设备是源设备
              const sourceCoords = this.getConnectionPointCoordinates(
                this.canvasDevices[deviceIndex],
                connection.sourcePoint,
                idx + 1,
                otherDevice
              );
              connection.sourceX = sourceCoords.x;
              connection.sourceY = sourceCoords.y;
            } else {
              // 当前设备是目标设备
              const targetCoords = this.getConnectionPointCoordinates(
                this.canvasDevices[deviceIndex],
                connection.targetPoint,
                idx + 1,
                otherDevice
              );
              connection.targetX = targetCoords.x;
              connection.targetY = targetCoords.y;
            }
            
            // 更新另一端的坐标
            if (isSource) {
              // 更新目标端坐标
              const targetCoords = this.getConnectionPointCoordinates(
                otherDevice,
                connection.targetPoint,
                idx + 1,
                this.canvasDevices[deviceIndex]
              );
              connection.targetX = targetCoords.x;
              connection.targetY = targetCoords.y;
            } else {
              // 更新源端坐标
              const sourceCoords = this.getConnectionPointCoordinates(
                otherDevice,
                connection.sourcePoint,
                idx + 1,
                this.canvasDevices[deviceIndex]
              );
              connection.sourceX = sourceCoords.x;
              connection.sourceY = sourceCoords.y;
            }
          });
        });
      }
    },
    
    stopDrag() {
      if (this.isDragging) {
        // 如果设备位置发生了变化，添加历史记录
        this.addToHistory();
        
        // 清除辅助线
        this.clearAlignmentGuides();
      }
      
      this.isDragging = false;
      // 移除事件监听器
      window.removeEventListener('mousemove', this.onDrag);
      window.removeEventListener('mouseup', this.stopDrag);
    },
    
    // 清除辅助线
    clearAlignmentGuides() {
      this.alignmentGuides.showGuides = false;
      this.alignmentGuides.horizontalGuides = [];
      this.alignmentGuides.verticalGuides = [];
    },
    
    // 计算设备对齐辅助线
    calculateAlignmentGuides(x, y, currentDeviceIndex) {
      // 初始化返回结果
      const result = {
        x: x,
        y: y,
        hasGuides: false,
        horizontalGuides: [],
        verticalGuides: []
      };
      
      // 如果没有启用辅助线，直接返回原始坐标
      if (!this.alignmentGuides.enabled) {
        return result;
      }
      
      // 获取当前设备
      const currentDevice = this.canvasDevices[currentDeviceIndex];
      if (!currentDevice) return result;
      
      // 设备的中心点和边缘点
      const currentWidth = 80; // 设备宽度
      const currentHeight = 80; // 设备高度
      
      // 当前设备的中心点和边缘点
      const currentLeft = x;
      const currentRight = x + currentWidth;
      const currentTop = y;
      const currentBottom = y + currentHeight;
      const currentCenterX = x + currentWidth / 2;
      const currentCenterY = y + currentHeight / 2;
      
      // 对齐阈值
      const threshold = this.alignmentGuides.threshold;
      
      // 水平和垂直对齐点
      let horizontalAlignments = [];
      let verticalAlignments = [];
      
      // 遍历其他设备
      this.canvasDevices.forEach((device, index) => {
        // 跳过当前设备
        if (index === currentDeviceIndex) return;
        
        // 其他设备的中心点和边缘点
        const otherLeft = device.x;
        const otherRight = device.x + currentWidth;
        const otherTop = device.y;
        const otherBottom = device.y + currentHeight;
        const otherCenterX = device.x + currentWidth / 2;
        const otherCenterY = device.y + currentHeight / 2;
        
        // 检查水平对齐点
        // 左边缘对齐
        if (Math.abs(currentLeft - otherLeft) < threshold) {
          horizontalAlignments.push({
            position: otherLeft,
            distance: Math.abs(currentLeft - otherLeft),
            type: 'left-to-left'
          });
        }
        
        // 右边缘对齐
        if (Math.abs(currentRight - otherRight) < threshold) {
          horizontalAlignments.push({
            position: otherRight - currentWidth,
            distance: Math.abs(currentRight - otherRight),
            type: 'right-to-right'
          });
        }
        
        // 左边缘对右边缘
        if (Math.abs(currentLeft - otherRight) < threshold) {
          horizontalAlignments.push({
            position: otherRight,
            distance: Math.abs(currentLeft - otherRight),
            type: 'left-to-right'
          });
        }
        
        // 右边缘对左边缘
        if (Math.abs(currentRight - otherLeft) < threshold) {
          horizontalAlignments.push({
            position: otherLeft - currentWidth,
            distance: Math.abs(currentRight - otherLeft),
            type: 'right-to-left'
          });
        }
        
        // 中心对齐
        if (Math.abs(currentCenterX - otherCenterX) < threshold) {
          horizontalAlignments.push({
            position: otherCenterX - currentWidth / 2,
            distance: Math.abs(currentCenterX - otherCenterX),
            type: 'center-to-center-x'
          });
        }
        
        // 检查垂直对齐点
        // 上边缘对齐
        if (Math.abs(currentTop - otherTop) < threshold) {
          verticalAlignments.push({
            position: otherTop,
            distance: Math.abs(currentTop - otherTop),
            type: 'top-to-top'
          });
        }
        
        // 下边缘对齐
        if (Math.abs(currentBottom - otherBottom) < threshold) {
          verticalAlignments.push({
            position: otherBottom - currentHeight,
            distance: Math.abs(currentBottom - otherBottom),
            type: 'bottom-to-bottom'
          });
        }
        
        // 上边缘对下边缘
        if (Math.abs(currentTop - otherBottom) < threshold) {
          verticalAlignments.push({
            position: otherBottom,
            distance: Math.abs(currentTop - otherBottom),
            type: 'top-to-bottom'
          });
        }
        
        // 下边缘对上边缘
        if (Math.abs(currentBottom - otherTop) < threshold) {
          verticalAlignments.push({
            position: otherTop - currentHeight,
            distance: Math.abs(currentBottom - otherTop),
            type: 'bottom-to-top'
          });
        }
        
        // 中心对齐
        if (Math.abs(currentCenterY - otherCenterY) < threshold) {
          verticalAlignments.push({
            position: otherCenterY - currentHeight / 2,
            distance: Math.abs(currentCenterY - otherCenterY),
            type: 'center-to-center-y'
          });
        }
      });
      
      // 遍历图形
      this.shapes.forEach((shape) => {
        // 获取图形的边界
        let shapeLeft, shapeRight, shapeTop, shapeBottom, shapeCenterX, shapeCenterY;
        
        if (shape.type === 'rectangle' || shape.type === 'circle') {
          shapeLeft = Math.min(shape.x1, shape.x2);
          shapeRight = Math.max(shape.x1, shape.x2);
          shapeTop = Math.min(shape.y1, shape.y2);
          shapeBottom = Math.max(shape.y1, shape.y2);
          shapeCenterX = (shape.x1 + shape.x2) / 2;
          shapeCenterY = (shape.y1 + shape.y2) / 2;
        } else if (shape.type === 'line') {
          // 线条的边界
          shapeLeft = Math.min(shape.x1, shape.x2);
          shapeRight = Math.max(shape.x1, shape.x2);
          shapeTop = Math.min(shape.y1, shape.y2);
          shapeBottom = Math.max(shape.y1, shape.y2);
          shapeCenterX = (shape.x1 + shape.x2) / 2;
          shapeCenterY = (shape.y1 + shape.y2) / 2;
        } else {
          return; // 跳过未知类型的图形
        }
        
        // 检查水平对齐
        if (Math.abs(currentLeft - shapeLeft) < threshold) {
          horizontalAlignments.push({
            position: shapeLeft,
            distance: Math.abs(currentLeft - shapeLeft),
            type: 'left-to-shape-left'
          });
        }
        
        if (Math.abs(currentRight - shapeRight) < threshold) {
          horizontalAlignments.push({
            position: shapeRight - currentWidth,
            distance: Math.abs(currentRight - shapeRight),
            type: 'right-to-shape-right'
          });
        }
        
        if (Math.abs(currentCenterX - shapeCenterX) < threshold) {
          horizontalAlignments.push({
            position: shapeCenterX - currentWidth / 2,
            distance: Math.abs(currentCenterX - shapeCenterX),
            type: 'center-to-shape-center-x'
          });
        }
        
        // 检查垂直对齐
        if (Math.abs(currentTop - shapeTop) < threshold) {
          verticalAlignments.push({
            position: shapeTop,
            distance: Math.abs(currentTop - shapeTop),
            type: 'top-to-shape-top'
          });
        }
        
        if (Math.abs(currentBottom - shapeBottom) < threshold) {
          verticalAlignments.push({
            position: shapeBottom - currentHeight,
            distance: Math.abs(currentBottom - shapeBottom),
            type: 'bottom-to-shape-bottom'
          });
        }
        
        if (Math.abs(currentCenterY - shapeCenterY) < threshold) {
          verticalAlignments.push({
            position: shapeCenterY - currentHeight / 2,
            distance: Math.abs(currentCenterY - shapeCenterY),
            type: 'center-to-shape-center-y'
          });
        }
      });
      
      // 找出最近的水平和垂直对齐点
      let closestHorizontal = null;
      let closestVertical = null;
      
      if (horizontalAlignments.length > 0) {
        horizontalAlignments.sort((a, b) => a.distance - b.distance);
        closestHorizontal = horizontalAlignments[0];
        result.x = closestHorizontal.position;
        result.hasGuides = true;
        
        // 根据对齐类型设置辅助线
        switch (closestHorizontal.type) {
          case 'left-to-left':
          case 'left-to-right':
          case 'left-to-shape-left':
            result.verticalGuides.push(result.x);
            break;
          case 'right-to-right':
          case 'right-to-left':
          case 'right-to-shape-right':
            result.verticalGuides.push(result.x + currentWidth);
            break;
          case 'center-to-center-x':
          case 'center-to-shape-center-x':
            result.verticalGuides.push(result.x + currentWidth / 2);
            break;
        }
      }
      
      if (verticalAlignments.length > 0) {
        verticalAlignments.sort((a, b) => a.distance - b.distance);
        closestVertical = verticalAlignments[0];
        result.y = closestVertical.position;
        result.hasGuides = true;
        
        // 根据对齐类型设置辅助线
        switch (closestVertical.type) {
          case 'top-to-top':
          case 'top-to-bottom':
          case 'top-to-shape-top':
            result.horizontalGuides.push(result.y);
            break;
          case 'bottom-to-bottom':
          case 'bottom-to-top':
          case 'bottom-to-shape-bottom':
            result.horizontalGuides.push(result.y + currentHeight);
            break;
          case 'center-to-center-y':
          case 'center-to-shape-center-y':
            result.horizontalGuides.push(result.y + currentHeight / 2);
            break;
        }
      }
      
            return result;
    },
    
    // 计算图形对齐辅助线
    calculateShapeAlignmentGuides(x1, y1, x2, y2) {
      // 初始化返回结果
      const result = {
        x1: x1,
        y1: y1,
        x2: x2,
        y2: y2,
        alignedX: false,
        alignedY: false,
        hasGuides: false,
        horizontalGuides: [],
        verticalGuides: []
      };
      
      // 如果没有启用辅助线，直接返回原始坐标
      if (!this.alignmentGuides.enabled) {
        return result;
      }
      
      // 计算图形的边界和中心点
      const shapeLeft = Math.min(x1, x2);
      const shapeRight = Math.max(x1, x2);
      const shapeTop = Math.min(y1, y2);
      const shapeBottom = Math.max(y1, y2);
      const shapeCenterX = (x1 + x2) / 2;
      const shapeCenterY = (y1 + y2) / 2;
      const shapeWidth = shapeRight - shapeLeft;
      const shapeHeight = shapeBottom - shapeTop;
      
      // 对齐阈值
      const threshold = this.alignmentGuides.threshold;
      
      // 水平和垂直对齐点
      let horizontalAlignments = [];
      let verticalAlignments = [];
      
      // 遍历设备
      this.canvasDevices.forEach(device => {
        // 设备的边界和中心点
        const deviceLeft = device.x;
        const deviceRight = device.x + 80; // 设备宽度
        const deviceTop = device.y;
        const deviceBottom = device.y + 80; // 设备高度
        const deviceCenterX = device.x + 40;
        const deviceCenterY = device.y + 40;
        
        // 检查水平对齐
        // 左边缘对齐
        if (Math.abs(shapeLeft - deviceLeft) < threshold) {
          verticalAlignments.push({
            position: deviceLeft,
            distance: Math.abs(shapeLeft - deviceLeft),
            type: 'left',
            alignTo: shapeLeft
          });
        }
        
        // 右边缘对齐
        if (Math.abs(shapeRight - deviceRight) < threshold) {
          verticalAlignments.push({
            position: deviceRight,
            distance: Math.abs(shapeRight - deviceRight),
            type: 'right',
            alignTo: shapeRight
          });
        }
        
        // 中心对齐
        if (Math.abs(shapeCenterX - deviceCenterX) < threshold) {
          verticalAlignments.push({
            position: deviceCenterX,
            distance: Math.abs(shapeCenterX - deviceCenterX),
            type: 'center-x',
            alignTo: shapeCenterX
          });
        }
        
        // 检查垂直对齐
        // 上边缘对齐
        if (Math.abs(shapeTop - deviceTop) < threshold) {
          horizontalAlignments.push({
            position: deviceTop,
            distance: Math.abs(shapeTop - deviceTop),
            type: 'top',
            alignTo: shapeTop
          });
        }
        
        // 下边缘对齐
        if (Math.abs(shapeBottom - deviceBottom) < threshold) {
          horizontalAlignments.push({
            position: deviceBottom,
            distance: Math.abs(shapeBottom - deviceBottom),
            type: 'bottom',
            alignTo: shapeBottom
          });
        }
        
        // 中心对齐
        if (Math.abs(shapeCenterY - deviceCenterY) < threshold) {
          horizontalAlignments.push({
            position: deviceCenterY,
            distance: Math.abs(shapeCenterY - deviceCenterY),
            type: 'center-y',
            alignTo: shapeCenterY
          });
        }
      });
      
      // 遍历其他图形
      this.shapes.forEach(shape => {
        // 跳过当前正在编辑的图形
        if (this.editingShape && shape.id === this.editingShape.id) return;
        
        // 获取图形的边界
        let otherLeft, otherRight, otherTop, otherBottom, otherCenterX, otherCenterY;
        
        if (shape.type === 'rectangle' || shape.type === 'circle') {
          otherLeft = Math.min(shape.x1, shape.x2);
          otherRight = Math.max(shape.x1, shape.x2);
          otherTop = Math.min(shape.y1, shape.y2);
          otherBottom = Math.max(shape.y1, shape.y2);
          otherCenterX = (shape.x1 + shape.x2) / 2;
          otherCenterY = (shape.y1 + shape.y2) / 2;
        } else if (shape.type === 'line') {
          otherLeft = Math.min(shape.x1, shape.x2);
          otherRight = Math.max(shape.x1, shape.x2);
          otherTop = Math.min(shape.y1, shape.y2);
          otherBottom = Math.max(shape.y1, shape.y2);
          otherCenterX = (shape.x1 + shape.x2) / 2;
          otherCenterY = (shape.y1 + shape.y2) / 2;
        } else {
          return; // 跳过未知类型的图形
        }
        
        // 检查水平对齐
        // 左边缘对齐
        if (Math.abs(shapeLeft - otherLeft) < threshold) {
          verticalAlignments.push({
            position: otherLeft,
            distance: Math.abs(shapeLeft - otherLeft),
            type: 'left',
            alignTo: shapeLeft
          });
        }
        
        // 右边缘对齐
        if (Math.abs(shapeRight - otherRight) < threshold) {
          verticalAlignments.push({
            position: otherRight,
            distance: Math.abs(shapeRight - otherRight),
            type: 'right',
            alignTo: shapeRight
          });
        }
        
        // 中心对齐
        if (Math.abs(shapeCenterX - otherCenterX) < threshold) {
          verticalAlignments.push({
            position: otherCenterX,
            distance: Math.abs(shapeCenterX - otherCenterX),
            type: 'center-x',
            alignTo: shapeCenterX
          });
        }
        
        // 检查垂直对齐
        // 上边缘对齐
        if (Math.abs(shapeTop - otherTop) < threshold) {
          horizontalAlignments.push({
            position: otherTop,
            distance: Math.abs(shapeTop - otherTop),
            type: 'top',
            alignTo: shapeTop
          });
        }
        
        // 下边缘对齐
        if (Math.abs(shapeBottom - otherBottom) < threshold) {
          horizontalAlignments.push({
            position: otherBottom,
            distance: Math.abs(shapeBottom - otherBottom),
            type: 'bottom',
            alignTo: shapeBottom
          });
        }
        
        // 中心对齐
        if (Math.abs(shapeCenterY - otherCenterY) < threshold) {
          horizontalAlignments.push({
            position: otherCenterY,
            distance: Math.abs(shapeCenterY - otherCenterY),
            type: 'center-y',
            alignTo: shapeCenterY
          });
        }
      });
      
      // 找出最近的水平和垂直对齐点
      if (verticalAlignments.length > 0) {
        verticalAlignments.sort((a, b) => a.distance - b.distance);
        const closestVertical = verticalAlignments[0];
        
        // 计算需要移动的距离
        let deltaX = 0;
        
        switch (closestVertical.type) {
          case 'left':
            deltaX = closestVertical.position - shapeLeft;
            break;
          case 'right':
            deltaX = closestVertical.position - shapeRight;
            break;
          case 'center-x':
            deltaX = closestVertical.position - shapeCenterX;
            break;
        }
        
        // 应用水平对齐
        result.x1 = x1 + deltaX;
        result.x2 = x2 + deltaX;
        result.alignedX = true;
        result.hasGuides = true;
        
        // 添加垂直辅助线
        result.verticalGuides.push(closestVertical.position);
      }
      
      if (horizontalAlignments.length > 0) {
        horizontalAlignments.sort((a, b) => a.distance - b.distance);
        const closestHorizontal = horizontalAlignments[0];
        
        // 计算需要移动的距离
        let deltaY = 0;
        
        switch (closestHorizontal.type) {
          case 'top':
            deltaY = closestHorizontal.position - shapeTop;
            break;
          case 'bottom':
            deltaY = closestHorizontal.position - shapeBottom;
            break;
          case 'center-y':
            deltaY = closestHorizontal.position - shapeCenterY;
            break;
        }
        
        // 应用垂直对齐
        result.y1 = y1 + deltaY;
        result.y2 = y2 + deltaY;
        result.alignedY = true;
        result.hasGuides = true;
        
        // 添加水平辅助线
        result.horizontalGuides.push(closestHorizontal.position);
      }
      
      return result;
    },
    
    onCanvasMouseDown(event) {
      if (this.currentTool === 'connect') {
        this.isCreatingConnection = true;
        this.newConnectionStart = { x: event.clientX, y: event.clientY };
      }
    },

    onCanvasMouseMove(event) {
      if (this.isCreatingConnection && this.newConnectionStart) {
        this.newConnectionEnd = { x: event.clientX, y: event.clientY };
      }
    },

    onCanvasMouseUp(event) {
      if (this.isCreatingConnection && this.newConnectionStart && this.newConnectionEnd) {
        const start = this.newConnectionStart;
        const end = this.newConnectionEnd;
        this.newConnectionStart = null;
        this.newConnectionEnd = null;
        this.isCreatingConnection = false;
        this.createConnection(start, end);
      }
    },

    onCanvasClick(event) {
      console.log('Canvas click target:', event.target.tagName, event.target.classList);
      
      // 检查是否点击在SVG形状或控制点上
      const isShapeElement = event.target.tagName === 'rect' || 
                             event.target.tagName === 'ellipse' || 
                             event.target.tagName === 'line' || 
                             event.target.tagName === 'circle';
      
      // 检查是否点击在控制点上
      const isControlPoint = event.target.classList && 
                            (event.target.classList.contains('control-point') || 
                             event.target.parentElement && event.target.parentElement.classList.contains('control-point'));
      
      // 检查是否点击在连线上
      const isConnectionElement = event.target.tagName === 'path' && 
                                 event.target.classList.contains('connection-path');
      
      // 如果点击的是形状元素、控制点或连线，不处理
      if (isShapeElement || isControlPoint || isConnectionElement) {
        console.log('点击了形状元素、控制点或连线，跳过画布点击处理');
        return;
      }
      
      // 如果点击的是画布或SVG区域（但不是形状、控制点或连线）
      console.log('点击了非形状/连线区域');
      
      // 如果当前有编辑状态，清除编辑状态
      if (this.editingShape) {
        console.log('退出图形编辑模式');
        this.editingShape = null;
        this.originalShapeData = null;
        this.showToast('已退出图形编辑模式');
      }
      
      // 检查是否点击在设备上或连线相关元素上
      const isDeviceElement = event.target.closest('.canvas-device');
      const isConnectionRelated = isConnectionElement || 
                                 event.target.classList.contains('port-label') ||
                                 event.target.classList.contains('port-label-box') ||
                                 event.target.classList.contains('connection-label');
      
      // 只有在不是点击设备或连线相关元素时，才清除选中状态
      if (!isDeviceElement && !isConnectionRelated) {
        this.selectedItem = null;
      }
      
      // 如果正在创建连接，但点击了画布，则取消连接
      if (this.isCreatingConnection) {
        this.isCreatingConnection = false;
        this.newConnectionStart = null;
        this.newConnectionEnd = null;
        
        // 移除事件监听器
        window.removeEventListener('mousemove', this.onConnectionDrag);
        window.removeEventListener('mouseup', this.finishConnection);
      }
      
      // 如果正在拖动形状或控制点，也取消拖动
      if (this.isShapeDragging || this.isControlPointDragging) {
        this.isShapeDragging = false;
        this.isControlPointDragging = false;
        this.draggedControlPoint = null;
        
        // 移除事件监听器
        window.removeEventListener('mousemove', this.onShapeDrag);
        window.removeEventListener('mouseup', this.stopShapeDrag);
        window.removeEventListener('mousemove', this.onControlPointDrag);
        window.removeEventListener('mouseup', this.stopControlPointDrag);
      }
    },
    
    // 处理画布双击事件
    onCanvasDblClick(event) {
      console.log('Canvas double click target:', event.target.tagName, event.target.classList);
      
      // 检查是否双击在SVG形状上
      const isShapeElement = event.target.tagName === 'rect' || 
                           event.target.tagName === 'ellipse' || 
                           event.target.tagName === 'line';
      
      if (isShapeElement) {
        console.log('双击了形状元素，但事件冒泡到了画布 - 这不应该发生');
        // 这不应该发生，因为我们已经在形状上阻止了事件冒泡
        // 如果这里被执行，说明事件冒泡没有被正确阻止
        event.stopPropagation();
        event.preventDefault();
        return;
      }
      
      // 处理在画布空白处双击的情况
      if (event.target.classList.contains('canvas-content')) {
        console.log('双击了画布空白区域');
        
        // 退出任何编辑模式
        if (this.editingShape) {
          console.log('退出图形编辑模式');
          this.editingShape = null;
          this.originalShapeData = null;
          this.showToast('已退出图形编辑模式');
        }
        
        this.selectedItem = null;
      }
    },

    createConnection(start, end) {
      const connection = {
        id: `${Date.now()}`,
        start: start,
        end: end,
        label: '',
        path: this.getPath(start, end)
      };
      this.connections.push(connection);
    },

    getPath(start, end) {
      // 实现路径计算逻辑
      // 这里可以根据需要选择不同的路径算法
      // 这里简单地返回一条直线
      return `M${start.x},${start.y} L${end.x},${end.y}`;
    },

    getConnectionPath(connection) {
      if (!connection.sourceX || !connection.sourceY || !connection.targetX || !connection.targetY) {
        return '';
      }
      
      const { sourceX, sourceY, targetX, targetY } = connection;
      
      // 计算连接线的方向
      const dx = targetX - sourceX;
      const dy = targetY - sourceY;
      const isHorizontalDominant = Math.abs(dx) >= Math.abs(dy);
      
      // 根据连接类型生成不同的路径
      switch (connection.type || this.pathStyle) {
        case '直线':
          return `M ${sourceX} ${sourceY} L ${targetX} ${targetY}`;
        
        case '曲线':
          // 生成贝塞尔曲线
          const controlX1 = sourceX + dx / 2;
          const controlY1 = sourceY;
          const controlX2 = sourceX + dx / 2;
          const controlY2 = targetY;
          
          return `M ${sourceX} ${sourceY} C ${controlX1} ${controlY1}, ${controlX2} ${controlY2}, ${targetX} ${targetY}`;
        
        case '正交线':
        default:
          // 生成正交线（直角连接）
          // 根据主导方向决定拐点位置
          if (isHorizontalDominant) {
            const midX = (sourceX + targetX) / 2;
            return `M ${sourceX} ${sourceY} L ${midX} ${sourceY} L ${midX} ${targetY} L ${targetX} ${targetY}`;
          } else {
            const midY = (sourceY + targetY) / 2;
            return `M ${sourceX} ${sourceY} L ${sourceX} ${midY} L ${targetX} ${midY} L ${targetX} ${targetY}`;
          }
      }
    },
    
    getNewConnectionPath() {
      if (!this.newConnectionStart || !this.newConnectionEnd) {
        console.log('无法获取临时连接路径：起点或终点未定义');
        return '';
      }
      
      const { x: sourceX, y: sourceY } = this.newConnectionStart;
      const { x: targetX, y: targetY } = this.newConnectionEnd;
      
      console.log('临时连接路径:', sourceX, sourceY, '->', targetX, targetY);
      
      // 计算连接线的方向
      const dx = targetX - sourceX;
      const dy = targetY - sourceY;
      const isHorizontalDominant = Math.abs(dx) >= Math.abs(dy);
      
      // 使用与getConnectionPath相同的逻辑，但用于临时连接
      switch (this.pathStyle) {
        case '直线':
          return `M ${sourceX} ${sourceY} L ${targetX} ${targetY}`;
        
        case '曲线':
          const controlX1 = sourceX + dx / 2;
          const controlY1 = sourceY;
          const controlX2 = sourceX + dx / 2;
          const controlY2 = targetY;
          
          return `M ${sourceX} ${sourceY} C ${controlX1} ${controlY1}, ${controlX2} ${controlY2}, ${targetX} ${targetY}`;
        
        case '正交线':
        default:
          // 根据主导方向决定拐点位置
          if (isHorizontalDominant) {
            const midX = (sourceX + targetX) / 2;
            return `M ${sourceX} ${sourceY} L ${midX} ${sourceY} L ${midX} ${targetY} L ${targetX} ${targetY}`;
          } else {
            const midY = (sourceY + targetY) / 2;
            return `M ${sourceX} ${sourceY} L ${sourceX} ${midY} L ${targetX} ${midY} L ${targetX} ${targetY}`;
          }
      }
    },
    
    getConnectionLabelPosition(connection) {
      // 如果连接有自定义标签位置，则使用它
      if (connection.labelX !== undefined && connection.labelY !== undefined) {
        return {
          x: connection.labelX,
          y: connection.labelY
        };
      }
      
      // 否则计算连接线的中点位置，用于放置标签
      const { sourceX, sourceY, targetX, targetY } = connection;
      
      // 根据连接类型计算标签位置
      switch (connection.type || this.pathStyle) {
        case '直线':
          return {
            x: (sourceX + targetX) / 2,
            y: (sourceY + targetY) / 2 - 10 // 向上偏移一点，避免标签与线重叠
          };
        
        case '曲线':
          return {
            x: (sourceX + targetX) / 2,
            y: (sourceY + targetY) / 2 - 15 // 曲线需要更多偏移
          };
        
        case '正交线':
        default:
          // 对于正交线，标签放在水平线段上
          const midX = (sourceX + targetX) / 2;
          const midY = (sourceY + targetY) / 2;
          
          // 判断哪个部分是水平的
          if (Math.abs(targetY - sourceY) > Math.abs(targetX - sourceX)) {
            // 垂直方向距离大，水平段在中间
            return {
              x: midX + 10, // 向右偏移一点
              y: midY
            };
          } else {
            // 水平方向距离大，水平段较长
            return {
              x: midX,
              y: sourceY - 10 // 向上偏移一点
            };
          }
      }
    },

    selectConnection(connection, event) {
      if (this.currentTool !== 'select' && this.currentTool !== 'edit') return;
      
      // 阻止事件冒泡，防止触发画布的点击事件
      if (event) {
        event.stopPropagation();
      }
      
      console.log('选中连线:', connection.id);
      this.selectedItem = connection;
    },

    onDeviceMouseDown(event, device) {
      console.log('设备点击:', device.name, '当前工具:', this.currentTool);
      
      // 如果是绘图模式，先退出绘图模式
      if (this.isDrawingMode) {
        this.isDrawingMode = false;
        this.drawingShape = null;
        document.body.style.cursor = 'default';
      }
      
      // 检测是否是双击
      if (event.detail === 2) {
        // 双击编辑设备
        console.log('双击编辑设备:', device.name);
        this.selectedItem = device;
        return;
      }
      
      // 如果当前工具是连接工具，开始创建连接
      if (this.currentTool === 'connect') {
        console.log('当前是连接工具，开始创建连接');
        
        // 显示端口选择对话框
        this.showPortSelectionDialog(device, 'center');
        return;
      }
      
      // 如果当前工具是选择工具，选中设备
      if (this.currentTool === 'select' || this.currentTool === 'edit') {
        this.selectedItem = device;
      }
      
      // 如果当前工具是删除工具，删除设备
      if (this.currentTool === 'delete') {
        this.deleteDevice(device);
        return;
      }
      
      // 如果当前工具是移动工具或选择工具，启用拖动
      if (this.currentTool === 'move' || this.currentTool === 'select') {
        this.startDrag(event, device);
      }
    },
    
    // 处理设备连接结束
    onDeviceConnectionEnd(event) {
      if (!this.isCreatingConnection || !this.newConnectionStart) {
        return;
      }
      
      // 获取鼠标位置
      const canvasRect = document.querySelector('.canvas-content').getBoundingClientRect();
      const mouseX = event.clientX - canvasRect.left;
      const mouseY = event.clientY - canvasRect.top;
      
      // 查找鼠标下的设备
      let targetDevice = null;
      for (const device of this.canvasDevices) {
        if (
          mouseX >= device.x && 
          mouseX <= device.x + 80 && 
          mouseY >= device.y && 
          mouseY <= device.y + 80
        ) {
          targetDevice = device;
          break;
        }
      }
      
      // 如果找到目标设备，并且不是同一个设备
      if (targetDevice && targetDevice.id !== this.newConnectionStart.device.id) {
        console.log('找到目标设备:', targetDevice.name);
        
        // 获取目标设备中心点
        const targetCenterX = targetDevice.x + 40;
        const targetCenterY = targetDevice.y + 40;
        
        // 创建新连接
        const newConnection = {
          id: `connection-${Date.now()}`,
          source: this.newConnectionStart.device.id,
          sourcePoint: 'center',
          target: targetDevice.id,
          targetPoint: 'center',
          sourceX: this.newConnectionStart.x,
          sourceY: this.newConnectionStart.y,
          targetX: targetCenterX,
          targetY: targetCenterY,
          type: this.pathStyle,
          label: `连接 ${this.connections.length + 1}`
        };
        
        this.connections.push(newConnection);
        console.log('创建新连接成功:', newConnection);
      } else {
        console.log('未找到目标设备或尝试连接到同一设备');
      }
      
      // 重置连接状态
      this.isCreatingConnection = false;
      this.newConnectionStart = null;
      this.newConnectionEnd = null;
      
      // 移除事件监听器
      window.removeEventListener('mousemove', this.onConnectionDrag);
      window.removeEventListener('mouseup', this.onDeviceConnectionEnd);
    },

    deleteDevice(device) {
      // 添加历史记录
      this.addToHistory();
      
      // 删除与该设备相关的所有连接
      this.connections = this.connections.filter(conn => 
        conn.source !== device.id && conn.target !== device.id
      );
      
      // 删除设备
      const index = this.canvasDevices.findIndex(d => d.id === device.id);
      if (index !== -1) {
        this.canvasDevices.splice(index, 1);
      }
      
      // 如果删除的是当前选中的设备，清除选中状态
      if (this.selectedItem && this.selectedItem.id === device.id) {
        this.selectedItem = null;
      }
    },

    deleteConnection(connection) {
      // 添加历史记录
      this.addToHistory();
      
      // 删除连接
      const index = this.connections.findIndex(c => c.id === connection.id);
      if (index !== -1) {
        this.connections.splice(index, 1);
      }
      
      // 清除选中状态
      if (this.selectedItem && this.selectedItem.id === connection.id) {
        this.selectedItem = null;
      }
    },

    updateDevice() {
      // 实现更新设备属性的逻辑
      console.log('设备属性已更新');
    },
    
    // 更新图形属性
    updateShape() {
      if (!this.selectedItem || !this.selectedItem.type) return;
      
      // 标记为有未保存的更改
      this.unsavedChanges = true;
      
      // 添加到历史记录
      this.addToHistory();
      
      console.log('图形属性已更新:', this.selectedItem);
    },

    updateConnection() {
      // 更新连接后重新计算路径
      if (this.selectedItem && this.selectedItem.source) {
        // 获取源设备和目标设备
        const sourceDevice = this.canvasDevices.find(d => d.id === this.selectedItem.source);
        const targetDevice = this.canvasDevices.find(d => d.id === this.selectedItem.target);
        
        if (sourceDevice && targetDevice) {
          // 找出源设备的所有连接及其索引
          const sourceConnections = this.connections.filter(conn => conn.source === sourceDevice.id);
          const sourceIndex = sourceConnections.indexOf(this.selectedItem) + 1;
          
          // 找出目标设备的所有连接及其索引
          const targetConnections = this.connections.filter(conn => conn.target === targetDevice.id);
          const targetIndex = targetConnections.indexOf(this.selectedItem) + 1;
          
          // 根据连接点类型和索引计算坐标
          const sourceCoords = this.getConnectionPointCoordinates(sourceDevice, this.selectedItem.sourcePoint, sourceIndex, targetDevice);
          const targetCoords = this.getConnectionPointCoordinates(targetDevice, this.selectedItem.targetPoint, targetIndex, sourceDevice);
          
          // 更新连接坐标
          this.selectedItem.sourceX = sourceCoords.x;
          this.selectedItem.sourceY = sourceCoords.y;
          this.selectedItem.targetX = targetCoords.x;
          this.selectedItem.targetY = targetCoords.y;
          
          // 不自动生成连接标签，保持原样
          // 如果用户在属性面板中手动设置了标签，则使用用户设置的标签
        }
      }
      
      console.log('连接属性已更新:', this.selectedItem);
    },

    startConnection(event, device, connectionPoint) {
      console.log('开始连接:', device.name, connectionPoint, '当前工具:', this.currentTool);
      
      // 如果当前工具不是连接工具，则不处理连接操作
      if (this.currentTool !== 'connect') {
        console.log('当前不是连接工具，不处理连接');
        return;
      }
      
      event.stopPropagation();
      
      // 显示端口选择对话框
      this.showPortSelectionDialog(device, 'center');
    },
    
    showPortSelectionDialog(device, connectionPoint) {
      this.portDialogDevice = device;
      this.selectedPort = '';
      this.showPortDialog = true;
      
      // 如果是第一个设备，记录这是连接的起点
      // 如果是第二个设备，这将在确认端口后处理
      if (!this.isFirstDeviceSelected) {
        this.connectionPoint = connectionPoint;
      }
      
      // 在下一个事件循环中聚焦输入框
      this.$nextTick(() => {
        if (this.$refs.portInput) {
          this.$refs.portInput.focus();
        }
      });
    },
    
    confirmPortSelection() {
      if (!this.selectedPort.trim()) {
        alert('请输入有效的端口号');
        return;
      }
      
      // 第一个设备选择处理
      if (!this.isFirstDeviceSelected) {
        // 保存第一个设备的数据
        this.isFirstDeviceSelected = true;
        
        // 保存第一个设备的数据
        this.firstDeviceData = {
          device: this.portDialogDevice,
          port: this.selectedPort,
          connectionPoint: this.connectionPoint
        };
        
        this.connectionInProgress = true;
        this.showPortDialog = false;
        
        // 显示连接进行中的状态
        this.isCreatingConnection = true;
        this.newConnectionStart = {
          x: this.portDialogDevice.x + 40, // 设备中心点
          y: this.portDialogDevice.y + 40,
          device: this.firstDeviceData.device,
          point: this.firstDeviceData.connectionPoint
        };
        
        console.log('已选择第一个设备:', this.firstDeviceData.device.name, '端口:', this.firstDeviceData.port);
      } else {
        // 第二个设备选择处理
        const secondDeviceData = {
          device: this.portDialogDevice,
          port: this.selectedPort,
          connectionPoint: this.connectionPoint
        };
        
        console.log('已选择第二个设备:', secondDeviceData.device.name, '端口:', secondDeviceData.port);
        
        // 添加历史记录
        this.addToHistory();
        
        // 获取这两个设备之间现有的连接
        const existingConnections = this.connections.filter(conn => 
          (conn.source === this.firstDeviceData.device.id && conn.target === secondDeviceData.device.id) ||
          (conn.source === secondDeviceData.device.id && conn.target === this.firstDeviceData.device.id)
        );
        
        // 按照端口号排序
        existingConnections.sort((a, b) => {
          const aPort = a.source === this.firstDeviceData.device.id ? a.sourcePort : a.targetPort;
          const bPort = b.source === this.firstDeviceData.device.id ? b.sourcePort : b.targetPort;
          return aPort.localeCompare(bPort);
        });
        
        // 计算新连接的索引
        const connectionIndex = existingConnections.length + 1;
        
        // 获取第一个和第二个设备的坐标，以便计算连接点
        const firstDeviceCoords = this.getConnectionPointCoordinates(
          this.firstDeviceData.device, 
          'center', 
          connectionIndex, 
          secondDeviceData.device
        );
        
        const secondDeviceCoords = this.getConnectionPointCoordinates(
          secondDeviceData.device, 
          'center', 
          connectionIndex, 
          this.firstDeviceData.device
        );
        
        // 创建新连接
        const newConnection = {
          id: `connection-${Date.now()}`,
          source: this.firstDeviceData.device.id,
          sourcePoint: 'center',
          sourcePort: this.firstDeviceData.port,
          target: secondDeviceData.device.id,
          targetPoint: 'center',
          targetPort: secondDeviceData.port,
          sourceX: firstDeviceCoords.x,
          sourceY: firstDeviceCoords.y,
          targetX: secondDeviceCoords.x,
          targetY: secondDeviceCoords.y,
          type: this.pathStyle,
          // 移除设备名称和端口号组合的标签
          label: ''
        };
        
        this.connections.push(newConnection);
        console.log('创建新连接成功:', newConnection);
        
        // 重置连接状态
        this.resetConnectionState();
        this.showPortDialog = false;
      }
    },
    
    cancelPortSelection() {
      this.showPortDialog = false;
      
      // 如果是第一个设备的取消，重置所有状态
      // 如果是第二个设备的取消，重置为第一个设备选择完成状态
      if (!this.isFirstDeviceSelected) {
        this.resetConnectionState();
      } else {
        // 只重置端口选择对话框状态，保持连接状态
        this.portDialogDevice = null;
        this.selectedPort = '';
      }
    },
    
    resetConnectionState() {
      this.isFirstDeviceSelected = false;
      this.firstDeviceData = null;
      this.connectionInProgress = false;
      this.portDialogDevice = null;
      this.selectedPort = '';
      this.isCreatingConnection = false;
      this.newConnectionStart = null;
      this.newConnectionEnd = null;
      
      // 移除事件监听器
      window.removeEventListener('mousemove', this.onConnectionDrag);
      window.removeEventListener('mouseup', this.finishConnection);
    },
    
    getConnectionPointCoordinates(device, pointType, connectionIndex = 0, targetDevice = null) {
      // 设备中心点
      const centerX = device.x + 40; // 设备宽度的一半
      const centerY = device.y + 40; // 设备高度的一半
      
      // 如果只有一条连接或者没有指定连接索引，使用中心点
      if (connectionIndex <= 1 && !targetDevice) {
        return { x: centerX, y: centerY };
      }
      
      // 计算连接线之间的偏移量
      const offsetStep = 10; // 每条线之间的偏移量（像素）
      
      // 如果提供了目标设备，计算连接线的角度
      if (targetDevice) {
        const targetCenterX = targetDevice.x + 40;
        const targetCenterY = targetDevice.y + 40;
        
        // 确定连接的顺序
        // 获取这两个设备之间的所有连接
        const devicePairConnections = this.connections.filter(conn => 
          (conn.source === device.id && conn.target === targetDevice.id) || 
          (conn.source === targetDevice.id && conn.target === device.id)
        );
        
        // 按照端口号排序
        devicePairConnections.sort((a, b) => {
          const aPort = a.source === device.id ? a.sourcePort : a.targetPort;
          const bPort = b.source === device.id ? b.sourcePort : b.targetPort;
          return aPort.localeCompare(bPort);
        });
        
        // 找出当前连接在排序后的连接列表中的索引
        let currentConnectionIndex = Math.min(connectionIndex - 1, devicePairConnections.length - 1);
        if (currentConnectionIndex < 0) currentConnectionIndex = 0;
        
        // 计算连接线的方向
        // 判断两个设备的相对位置，决定是水平还是垂直排列
        const dx = targetCenterX - centerX;
        const dy = targetCenterY - centerY;
        const isHorizontalDominant = Math.abs(dx) >= Math.abs(dy);
        
        // 计算连接线之间的偏移
        const totalConnections = devicePairConnections.length;
        const maxOffset = (totalConnections - 1) * offsetStep / 2;
        let offset = 0;
        
        if (totalConnections > 1) {
          // 均匀分布连接线，使它们平行且不交叉
          offset = currentConnectionIndex * offsetStep - maxOffset;
        }
        
        // 设备边缘偏移量，使连接线从设备边缘开始
        const edgeOffset = 25; // 设备半径大约为25像素
        
        // 根据主导方向计算偏移
        if (isHorizontalDominant) {
          // 水平方向为主，垂直方向偏移
          // 计算水平方向的偏移，使连接线从设备边缘开始
          const horizontalOffset = Math.sign(dx) * edgeOffset;
          return { 
            x: centerX + horizontalOffset, 
            y: centerY + offset 
          };
        } else {
          // 垂直方向为主，水平方向偏移
          // 计算垂直方向的偏移，使连接线从设备边缘开始
          const verticalOffset = Math.sign(dy) * edgeOffset;
          return { 
            x: centerX + offset, 
            y: centerY + verticalOffset 
          };
        }
      } else {
        // 如果没有目标设备，使用简单的水平和垂直偏移
        // 对于偶数索引，放在右侧；对于奇数索引，放在左侧
        const horizontalSide = connectionIndex % 2 === 0 ? 1 : -1;
        const horizontalMagnitude = Math.ceil(connectionIndex / 2) * offsetStep;
        const horizontalOffset = horizontalSide * horizontalMagnitude;
        
        // 对于索引大于2的连接，添加垂直偏移
        const verticalOffset = connectionIndex > 2 ? Math.floor((connectionIndex - 1) / 2) * offsetStep : 0;
        
        return { 
          x: centerX + horizontalOffset, 
          y: centerY + verticalOffset 
        };
      }
    },
    
    // 修改onConnectionDrag方法以适应新的连接逻辑
    onConnectionDrag(event) {
      if (!this.isCreatingConnection || !this.newConnectionStart) return;
      
      const canvasRect = document.querySelector('.canvas-content').getBoundingClientRect();
      
      // 更新连接终点为当前鼠标位置
      this.newConnectionEnd = {
        x: event.clientX - canvasRect.left,
        y: event.clientY - canvasRect.top
      };
      
      // 防止选择其他元素
      event.preventDefault();
      event.stopPropagation();
    },
    
    finishConnection(event) {
      // 如果没有连接到另一个设备的连接点，则取消连接
      if (this.isCreatingConnection) {
        this.isCreatingConnection = false;
        this.newConnectionStart = null;
        this.newConnectionEnd = null;
      }
      
      // 移除事件监听器
      window.removeEventListener('mousemove', this.onConnectionDrag);
      window.removeEventListener('mouseup', this.finishConnection);
    },
    
    // 修改显示连接标签的方法以显示端口信息
    getConnectionLabelPosition(connection) {
      // 如果连接有自定义标签位置，则使用它
      if (connection.labelX !== undefined && connection.labelY !== undefined) {
        return {
          x: connection.labelX,
          y: connection.labelY
        };
      }
      
      // 否则计算连接线的中点位置，用于放置标签
      const { sourceX, sourceY, targetX, targetY } = connection;
      
      // 根据连接类型计算标签位置
      switch (connection.type || this.pathStyle) {
        case '直线':
          return {
            x: (sourceX + targetX) / 2,
            y: (sourceY + targetY) / 2 - 10 // 向上偏移一点，避免标签与线重叠
          };
        
        case '曲线':
          return {
            x: (sourceX + targetX) / 2,
            y: (sourceY + targetY) / 2 - 15 // 曲线需要更多偏移
          };
        
        case '正交线':
        default:
          // 对于正交线，标签放在水平线段上
          const midX = (sourceX + targetX) / 2;
          const midY = (sourceY + targetY) / 2;
          
          // 判断哪个部分是水平的
          if (Math.abs(targetY - sourceY) > Math.abs(targetX - sourceX)) {
            // 垂直方向距离大，水平段在中间
            return {
              x: midX + 10, // 向右偏移一点
              y: midY
            };
          } else {
            // 水平方向距离大，水平段较长
            return {
              x: midX,
              y: sourceY - 10 // 向上偏移一点
            };
          }
      }
    },
    
    // 添加或修改连接属性面板，以显示和编辑端口信息
    updateConnection() {
      // 原有代码...
      
      console.log('连接属性已更新:', this.selectedItem);
    },

    // ... existing code ...

    // 开始拖动标签
    startLabelDrag(event, item, labelType = 'connection') {
      // 记录拖动的标签类型（设备标签、连线标签等）
      this.isDraggingLabel = true;
      this.draggedLabel = item;
      this.draggedLabelType = labelType;
      
      // 计算鼠标点击位置与标签左上角的偏移
      const labelElement = event.currentTarget;
      const labelRect = labelElement.getBoundingClientRect();
      
      this.dragLabelOffsetX = event.clientX - labelRect.left;
      this.dragLabelOffsetY = event.clientY - labelRect.top;
      
      // 添加鼠标移动和释放事件监听器
      window.addEventListener('mousemove', this.onLabelDrag);
      window.addEventListener('mouseup', this.stopLabelDrag);
      
      // 阻止事件冒泡和默认行为
      event.stopPropagation();
      event.preventDefault();
    },
    
    // 选中连线并开始拖动标签
    selectAndDragLabel(event, connection, labelType) {
      // 先选中连线
      this.selectConnection(connection, event);
      
      // 然后开始拖动标签
      this.startLabelDrag(event, connection, labelType);
    },
    
    // 拖动标签
    onLabelDrag(event) {
      if (!this.isDraggingLabel || !this.draggedLabel) return;
      
      // 获取画布位置
      const canvas = document.querySelector('.canvas-content');
      const canvasRect = canvas.getBoundingClientRect();
      
      // 计算新位置
      const newX = event.clientX - canvasRect.left;
      const newY = event.clientY - canvasRect.top;
      
      // 根据标签类型更新标签位置
      switch (this.draggedLabelType) {
        case 'source':
          this.draggedLabel.sourcePortLabelX = newX;
          this.draggedLabel.sourcePortLabelY = newY;
          break;
        case 'target':
          this.draggedLabel.targetPortLabelX = newX;
          this.draggedLabel.targetPortLabelY = newY;
          break;
        case 'device':
          // 更新设备标签位置
          const deviceCenterX = this.draggedLabel.x + 40;
          const deviceCenterY = this.draggedLabel.y + 40;
          
          // 计算相对于设备中心的偏移
          this.draggedLabel.labelOffsetX = newX - deviceCenterX;
          this.draggedLabel.labelOffsetY = newY - deviceCenterY;
          
          // 确保更新后的UI立即反映变化
          this.$forceUpdate();
          break;
        default: // 'connection'
          this.draggedLabel.labelX = newX;
          this.draggedLabel.labelY = newY;
          break;
      }
    },
    
    // 停止拖动标签
    stopLabelDrag() {
      // 添加历史记录
      if (this.isDraggingLabel) {
        this.addToHistory();
      }
      
      this.isDraggingLabel = false;
      this.draggedLabel = null;
      this.draggedLabelType = null;
      
      // 移除事件监听器
      window.removeEventListener('mousemove', this.onLabelDrag);
      window.removeEventListener('mouseup', this.stopLabelDrag);
    },
    
    // 获取源端口标签位置
    getSourcePortLabelPosition(connection) {
      // 如果连接有自定义源端口标签位置，则使用它
      if (connection.sourcePortLabelX !== undefined && connection.sourcePortLabelY !== undefined) {
        return {
          x: connection.sourcePortLabelX,
          y: connection.sourcePortLabelY
        };
      }
      
      // 否则计算源端口标签位置
      const { sourceX, sourceY, targetX, targetY } = connection;
      
      // 计算连接线的方向
      const dx = targetX - sourceX;
      const dy = targetY - sourceY;
      const isHorizontalDominant = Math.abs(dx) >= Math.abs(dy);
      
      // 获取源设备
      const sourceDevice = this.canvasDevices.find(d => d.id === connection.source);
      
      if (isHorizontalDominant) {
        // 水平方向为主，标签放在连接线上方
        // 向设备内侧移动标签
        const inwardDirection = Math.sign(dx); // 1表示向右，-1表示向左
        return {
          x: sourceX + inwardDirection * 35, // 向内侧移动35像素
          y: sourceY - 15
        };
      } else {
        // 垂直方向为主，标签放在连接线左侧
        // 向设备内侧移动标签
        const inwardDirection = Math.sign(dy); // 1表示向下，-1表示向上
        return {
          x: sourceX - 20,
          y: sourceY + inwardDirection * 35 // 向内侧移动35像素
        };
      }
    },
    
    // 获取目标端口标签位置
    getTargetPortLabelPosition(connection) {
      // 如果连接有自定义目标端口标签位置，则使用它
      if (connection.targetPortLabelX !== undefined && connection.targetPortLabelY !== undefined) {
        return {
          x: connection.targetPortLabelX,
          y: connection.targetPortLabelY
        };
      }
      
      // 否则计算目标端口标签位置
      const { sourceX, sourceY, targetX, targetY } = connection;
      
      // 计算连接线的方向
      const dx = targetX - sourceX;
      const dy = targetY - sourceY;
      const isHorizontalDominant = Math.abs(dx) >= Math.abs(dy);
      
      // 获取目标设备
      const targetDevice = this.canvasDevices.find(d => d.id === connection.target);
      
      if (isHorizontalDominant) {
        // 水平方向为主，标签放在连接线上方
        // 向设备内侧移动标签
        const inwardDirection = -Math.sign(dx); // -1表示向左，1表示向右
        return {
          x: targetX + inwardDirection * 35, // 向内侧移动35像素
          y: targetY - 15
        };
      } else {
        // 垂直方向为主，标签放在连接线左侧
        // 向设备内侧移动标签
        const inwardDirection = -Math.sign(dy); // -1表示向上，1表示向下
        return {
          x: targetX - 20,
          y: targetY + inwardDirection * 35 // 向内侧移动35像素
        };
      }
    },
    
    useGeneratedPort() {
      this.selectedPort = this.generatedPortId;
    },
    selectPort(port) {
      this.selectedPort = port.id;
      this.showPortDialog = false;
    },
    
    // 添加保存功能
    saveTopology() {
      // 如果已经有文件名，则直接保存（无需弹窗）
      if (this.topologyFileName) {
        this.saveTopologyToStorage();
        this.showToast('拓扑图已保存');
      } else {
        // 新拓扑图或未命名拓扑图，显示保存对话框
        this.showSaveDialog = true;
        
        // 生成默认文件名
        this.topologyFileName = `拓扑图-${new Date().toISOString().slice(0, 10)}`;
        
        // 聚焦到文件名输入框
        this.$nextTick(() => {
          if (this.$refs.saveNameInput) {
            this.$refs.saveNameInput.focus();
          }
        });
      }
    },
    
    // 显示消息提示
    showToast(message) {
      this.toastMessage = message;
      this.showToastMessage = true;
      
      // 3秒后自动隐藏
      setTimeout(() => {
        this.showToastMessage = false;
      }, 3000);
    },
    
    // 打开调色板
    openPalette() {
      // 如果当前处于绘图模式，先退出绘图模式
      if (this.isDrawingMode) {
        this.isDrawingMode = false;
        this.drawingShape = null;
        document.body.style.cursor = 'default';
      }
      
      // 如果有选中项目，根据选中项目类型设置初始选项
      if (this.selectedItem) {
        if (this.selectedItem.source) {
          // 连接线
          this.selectedBorderType = 'line';
          if (this.selectedItem.color) {
            this.currentColor = this.selectedItem.color;
          }
          if (this.selectedItem.strokeWidth) {
            this.borderWidth = this.selectedItem.strokeWidth;
          }
          if (this.selectedItem.strokeStyle) {
            this.selectedBorderStyle = this.selectedItem.strokeStyle;
          }
        } else if (this.selectedItem.type) {
          // 已保存的形状
          this.selectedBorderType = this.selectedItem.type;
          this.currentColor = this.selectedItem.color;
          this.borderWidth = this.selectedItem.strokeWidth;
          this.selectedBorderStyle = this.selectedItem.strokeStyle;
          this.isFilled = this.selectedItem.isFilled;
        } else {
          // 设备
          this.selectedBorderType = 'rectangle'; // 默认设备为矩形
          if (this.selectedItem.borderColor) {
            this.currentColor = this.selectedItem.borderColor;
          }
          if (this.selectedItem.borderWidth) {
            this.borderWidth = this.selectedItem.borderWidth;
          }
          if (this.selectedItem.borderStyle) {
            this.selectedBorderStyle = this.selectedItem.borderStyle;
          }
          // 设置填充状态
          this.isFilled = this.selectedItem.fillColor && this.selectedItem.fillColor !== 'transparent';
        }
      }
      
      // 显示调色板对话框
      this.showPaletteDialog = true;
    },
    
    // 选择形状
    selectShape(shape, event) {
      if (event) {
        event.stopPropagation();
        event.preventDefault();
      }
      
      console.log('选择形状:', shape.type);
      
      // 如果是绘图模式，先退出绘图模式
      if (this.isDrawingMode) {
        this.isDrawingMode = false;
        this.drawingShape = null;
        document.body.style.cursor = 'default';
      }
      
      // 如果当前工具是删除工具，则删除形状
      if (this.currentTool === 'delete') {
        this.deleteShape(shape);
        return;
      }
      
      this.selectedItem = shape;
      console.log('已选中形状:', shape);
    },
    
    // 设置形状编辑模式（双击形状时调用）
    setShapeEditMode(shape, event) {
      // 阻止事件冒泡和默认行为
      if (event) {
        event.stopPropagation();
        event.preventDefault();
        console.log('双击形状进入编辑模式:', shape.type, '形状ID:', shape.id);
      }
      
      // 如果形状已经在编辑模式，退出编辑模式
      if (this.editingShape === shape) {
        console.log('已在编辑模式，退出编辑模式');
        this.editingShape = null;
        this.originalShapeData = null;
        this.showToast('已退出图形编辑模式');
        return;
      }
      
      // 退出绘图模式（如果有）
      if (this.isDrawingMode) {
        this.isDrawingMode = false;
        this.drawingShape = null;
      }
      
      // 退出之前的编辑模式（如果有）
      if (this.editingShape) {
        console.log('退出之前的编辑模式:', this.editingShape.id);
        this.editingShape = null;
        this.originalShapeData = null;
      }
      
      // 设置当前编辑的形状并选中它
      this.editingShape = shape;
      this.selectedItem = shape;
      
      // 保存原始形状数据，用于后续操作
      this.originalShapeData = JSON.parse(JSON.stringify(shape));
      
      console.log('设置形状为编辑模式:', shape.id, '类型:', shape.type);
      this.showToast('已进入图形编辑模式，可拖动控制点调整大小或位置，点击空白区域或按ESC键退出');
    },
    
    // 开始拖动整个形状
    startShapeDrag(event) {
      if (!this.editingShape) return;
      
      event.stopPropagation();
      
      // 设置拖动状态
      this.isShapeDragging = true;
      
      // 获取画布位置
      const canvas = document.querySelector('.canvas-content');
      const canvasRect = canvas.getBoundingClientRect();
      
      // 考虑滚动位置和缩放比例
      const scrollLeft = canvas.scrollLeft;
      const scrollTop = canvas.scrollTop;
      const zoomFactor = this.zoom / 100;
      
      // 记录鼠标起始位置
      this.shapeDragStartX = (event.clientX - canvasRect.left + scrollLeft) / zoomFactor;
      this.shapeDragStartY = (event.clientY - canvasRect.top + scrollTop) / zoomFactor;
      
      // 添加鼠标移动和释放事件
      window.addEventListener('mousemove', this.onShapeDrag);
      window.addEventListener('mouseup', this.stopShapeDrag);
      
      // 保存初始形状数据
      this.originalShapeData = JSON.parse(JSON.stringify(this.editingShape));
    },
    
    // 拖动形状过程中
    onShapeDrag(event) {
      if (!this.isShapeDragging || !this.editingShape) return;
      
      // 获取画布位置
      const canvas = document.querySelector('.canvas-content');
      const canvasRect = canvas.getBoundingClientRect();
      
      // 考虑滚动位置和缩放比例
      const scrollLeft = canvas.scrollLeft;
      const scrollTop = canvas.scrollTop;
      const zoomFactor = this.zoom / 100;
      
      // 计算当前鼠标位置
      const currentX = (event.clientX - canvasRect.left + scrollLeft) / zoomFactor;
      const currentY = (event.clientY - canvasRect.top + scrollTop) / zoomFactor;
      
      // 计算偏移
      const deltaX = currentX - this.shapeDragStartX;
      const deltaY = currentY - this.shapeDragStartY;
      
      // 应用网格吸附
      let snappedDeltaX = deltaX;
      let snappedDeltaY = deltaY;
      
      if (this.gridEnabled) {
        snappedDeltaX = Math.round(deltaX / this.gridSize) * this.gridSize;
        snappedDeltaY = Math.round(deltaY / this.gridSize) * this.gridSize;
      }
      
      // 计算新位置
      let newX1 = this.originalShapeData.x1 + snappedDeltaX;
      let newY1 = this.originalShapeData.y1 + snappedDeltaY;
      let newX2 = this.originalShapeData.x2 + snappedDeltaX;
      let newY2 = this.originalShapeData.y2 + snappedDeltaY;
      
      // 如果启用了辅助线，计算对齐位置
      if (this.alignmentGuides.enabled) {
        const result = this.calculateShapeAlignmentGuides(newX1, newY1, newX2, newY2);
        
        // 应用对齐结果
        if (result.alignedX) {
          const deltaXAdjustment = result.x1 - newX1;
          newX1 = result.x1;
          newX2 = newX2 + deltaXAdjustment;
        }
        
        if (result.alignedY) {
          const deltaYAdjustment = result.y1 - newY1;
          newY1 = result.y1;
          newY2 = newY2 + deltaYAdjustment;
        }
        
        // 更新辅助线显示状态
        this.alignmentGuides.showGuides = result.hasGuides;
        this.alignmentGuides.horizontalGuides = result.horizontalGuides;
        this.alignmentGuides.verticalGuides = result.verticalGuides;
      }
      
      // 更新形状位置
      const shape = this.editingShape;
      shape.x1 = newX1;
      shape.y1 = newY1;
      shape.x2 = newX2;
      shape.y2 = newY2;
      
      // 标记为有未保存的更改
      this.unsavedChanges = true;
    },
    
    // 停止拖动形状
    stopShapeDrag() {
      if (!this.isShapeDragging) return;
      
      this.isShapeDragging = false;
      
      // 移除事件监听器
      window.removeEventListener('mousemove', this.onShapeDrag);
      window.removeEventListener('mouseup', this.stopShapeDrag);
      
      // 如果形状位置发生了变化，添加历史记录
      if (this.originalShapeData.x1 !== this.editingShape.x1 || 
          this.originalShapeData.y1 !== this.editingShape.y1) {
        this.addToHistory();
      }
      
      // 清除辅助线
      this.clearAlignmentGuides();
    },
    
    // 开始拖动控制点
    startControlPointDrag(event, pointType) {
      if (!this.editingShape) return;
      
      event.stopPropagation();
      
      // 设置拖动状态
      this.isControlPointDragging = true;
      this.draggedControlPoint = pointType;
      
      // 获取画布位置
      const canvas = document.querySelector('.canvas-content');
      const canvasRect = canvas.getBoundingClientRect();
      
      // 考虑滚动位置和缩放比例
      const scrollLeft = canvas.scrollLeft;
      const scrollTop = canvas.scrollTop;
      const zoomFactor = this.zoom / 100;
      
      // 记录鼠标起始位置
      this.shapeDragStartX = (event.clientX - canvasRect.left + scrollLeft) / zoomFactor;
      this.shapeDragStartY = (event.clientY - canvasRect.top + scrollTop) / zoomFactor;
      
      // 添加鼠标移动和释放事件
      window.addEventListener('mousemove', this.onControlPointDrag);
      window.addEventListener('mouseup', this.stopControlPointDrag);
      
      // 保存初始形状数据
      this.originalShapeData = JSON.parse(JSON.stringify(this.editingShape));
    },
    
    // 拖动控制点过程中
    onControlPointDrag(event) {
      if (!this.isControlPointDragging || !this.editingShape) return;
      
      // 获取画布位置
      const canvas = document.querySelector('.canvas-content');
      const canvasRect = canvas.getBoundingClientRect();
      
      // 考虑滚动位置和缩放比例
      const scrollLeft = canvas.scrollLeft;
      const scrollTop = canvas.scrollTop;
      const zoomFactor = this.zoom / 100;
      
      // 计算当前鼠标位置
      let currentX = (event.clientX - canvasRect.left + scrollLeft) / zoomFactor;
      let currentY = (event.clientY - canvasRect.top + scrollTop) / zoomFactor;
      
      // 应用网格吸附
      if (this.gridEnabled) {
        currentX = Math.round(currentX / this.gridSize) * this.gridSize;
        currentY = Math.round(currentY / this.gridSize) * this.gridSize;
      }
      
      const shape = this.editingShape;
      
      // 根据不同的控制点类型更新形状
      switch (this.draggedControlPoint) {
        // 直线控制点
        case 'start':
          shape.x1 = currentX;
          shape.y1 = currentY;
          break;
        case 'end':
          shape.x2 = currentX;
          shape.y2 = currentY;
          break;
          
        // 矩形/圆形角点
        case 'tl': // 左上角
          shape.x1 = Math.min(currentX, this.originalShapeData.x2);
          shape.y1 = Math.min(currentY, this.originalShapeData.y2);
          break;
        case 'tr': // 右上角
          shape.x2 = Math.max(currentX, this.originalShapeData.x1);
          shape.y1 = Math.min(currentY, this.originalShapeData.y2);
          break;
        case 'bl': // 左下角
          shape.x1 = Math.min(currentX, this.originalShapeData.x2);
          shape.y2 = Math.max(currentY, this.originalShapeData.y1);
          break;
        case 'br': // 右下角
          shape.x2 = Math.max(currentX, this.originalShapeData.x1);
          shape.y2 = Math.max(currentY, this.originalShapeData.y1);
          break;
      }
      
      // 标记为有未保存的更改
      this.unsavedChanges = true;
    },
    
    // 停止拖动控制点
    stopControlPointDrag() {
      if (!this.isControlPointDragging) return;
      
      this.isControlPointDragging = false;
      this.draggedControlPoint = null;
      
      // 移除事件监听器
      window.removeEventListener('mousemove', this.onControlPointDrag);
      window.removeEventListener('mouseup', this.stopControlPointDrag);
      
      // 添加历史记录
      this.addToHistory();
    },
    
    // 删除形状
    deleteShape(shape) {
      const index = this.shapes.findIndex(s => s.id === shape.id);
      if (index !== -1) {
        this.shapes.splice(index, 1);
        
        // 如果删除的是当前选中的形状，清除选中状态
        if (this.selectedItem && this.selectedItem.id === shape.id) {
          this.selectedItem = null;
        }
        
        // 标记为有未保存的更改
        this.unsavedChanges = true;
        this.addToHistory();
        
        this.showToast('形状已删除');
      }
    },
    
    // 处理键盘事件
    handleKeyDown(event) {
      console.log('键盘事件:', event.key);
      
      // 如果按下Delete键，并且有选中的形状，则删除形状
      if (event.key === 'Delete' && this.selectedItem && this.selectedItem.type) {
        this.deleteShape(this.selectedItem);
      }
      
      // 如果按下Escape键
      if (event.key === 'Escape') {
        // 如果在绘图模式，取消绘图模式
        if (this.isDrawingMode) {
          this.isDrawingMode = false;
          this.drawingShape = null;
          document.body.style.cursor = 'default';
          this.showToast('已取消绘制');
        }
        // 如果在编辑模式，退出编辑模式
        else if (this.editingShape) {
          console.log('按Escape键退出图形编辑模式');
          this.editingShape = null;
          this.originalShapeData = null;
          this.showToast('已退出图形编辑模式');
        }
        // 取消选中状态
        this.selectedItem = null;
      }
    },
    
    // 打开颜色选择器
    openColorPicker() {
      this.$nextTick(() => {
        if (this.$refs.colorInput) {
          this.$refs.colorInput.click();
        }
      });
    },
    
    // 取消调色板设置
    cancelPaletteSettings() {
      this.showPaletteDialog = false;
    },
    
    // 更新选中的颜色
    updateColor() {
      console.log('颜色已更新:', this.currentColor);
    },
    
    // 应用调色板设置
    applyPaletteSettings() {
      // 如果当前有选中的形状，更新形状属性
      if (this.selectedItem && this.selectedItem.type) {
        // 更新已保存形状的属性
        this.selectedItem.color = this.currentColor;
        this.selectedItem.strokeWidth = this.borderWidth;
        this.selectedItem.strokeStyle = this.selectedBorderStyle;
        this.selectedItem.isFilled = this.isFilled;
        
        // 标记为有未保存的更改
        this.unsavedChanges = true;
        this.addToHistory();
        
        // 关闭对话框
        this.showPaletteDialog = false;
        
        // 显示提示
        this.showToast('形状样式已更新');
      } else {
        // 关闭对话框
        this.showPaletteDialog = false;
        
        // 启用绘图模式
        this.isDrawingMode = true;
        this.drawingShape = this.selectedBorderType;
        
        // 重置绘制坐标，避免显示残留形状
        this.drawStartX = null;
        this.drawStartY = null;
        this.drawCurrentX = null;
        this.drawCurrentY = null;
        
        // 更新鼠标指针样式
        document.body.style.cursor = 'crosshair';
        
        // 显示提示
        this.showToast(`已启用${this.getBorderTypeName()}绘制模式，请在画布上绘制`);
      }
    },
    
    // 获取边框类型名称
    getBorderTypeName() {
      const type = this.borderTypes.find(t => t.id === this.selectedBorderType);
      return type ? type.name : '形状';
    },
    
    // 开始绘制
    startDrawing(event) {
      if (!this.isDrawingMode) return;
      
      // 获取画布相对位置
      const canvasRect = document.querySelector('.canvas-content').getBoundingClientRect();
      
      // 考虑滚动位置
      const scrollLeft = document.querySelector('.canvas-content').scrollLeft;
      const scrollTop = document.querySelector('.canvas-content').scrollTop;
      
      // 考虑缩放比例
      const zoomFactor = this.zoom / 100;
      
      // 记录起始点，考虑滚动位置和缩放
      this.drawStartX = (event.clientX - canvasRect.left + scrollLeft) / zoomFactor;
      this.drawStartY = (event.clientY - canvasRect.top + scrollTop) / zoomFactor;
      this.drawCurrentX = this.drawStartX;
      this.drawCurrentY = this.drawStartY;
      
      // 添加鼠标移动和释放事件
      window.addEventListener('mousemove', this.onDrawingMove);
      window.addEventListener('mouseup', this.finishDrawing);
      
      // 阻止默认行为和事件冒泡
      event.preventDefault();
      event.stopPropagation();
    },
    
    // 绘制过程中移动
    onDrawingMove(event) {
      if (!this.isDrawingMode) return;
      
      // 获取画布相对位置
      const canvasRect = document.querySelector('.canvas-content').getBoundingClientRect();
      
      // 考虑滚动位置
      const scrollLeft = document.querySelector('.canvas-content').scrollLeft;
      const scrollTop = document.querySelector('.canvas-content').scrollTop;
      
      // 考虑缩放比例
      const zoomFactor = this.zoom / 100;
      
      // 更新当前点，考虑缩放
      this.drawCurrentX = (event.clientX - canvasRect.left + scrollLeft) / zoomFactor;
      this.drawCurrentY = (event.clientY - canvasRect.top + scrollTop) / zoomFactor;
      
      // 阻止默认行为和事件冒泡
      event.preventDefault();
      event.stopPropagation();
    },
    
    // 完成绘制
    finishDrawing(event) {
      if (!this.isDrawingMode) return;
      
      // 获取画布相对位置
      const canvasRect = document.querySelector('.canvas-content').getBoundingClientRect();
      
      // 考虑滚动位置
      const scrollLeft = document.querySelector('.canvas-content').scrollLeft;
      const scrollTop = document.querySelector('.canvas-content').scrollTop;
      
      // 考虑缩放比例
      const zoomFactor = this.zoom / 100;
      
      // 更新最终点，考虑缩放
      this.drawCurrentX = (event.clientX - canvasRect.left + scrollLeft) / zoomFactor;
      this.drawCurrentY = (event.clientY - canvasRect.top + scrollTop) / zoomFactor;
      
      // 如果起点和终点相同或非常接近，则不创建形状
      const distance = Math.sqrt(
        Math.pow(this.drawCurrentX - this.drawStartX, 2) + 
        Math.pow(this.drawCurrentY - this.drawStartY, 2)
      );
      
      if (distance > 5) { // 最小距离阈值
        // 保存绘制的形状
        const newShape = {
          id: Date.now().toString(),
          type: this.drawingShape,
          x1: this.drawStartX,
          y1: this.drawStartY,
          x2: this.drawCurrentX,
          y2: this.drawCurrentY,
          color: this.currentColor,
          strokeWidth: this.borderWidth,
          strokeStyle: this.selectedBorderStyle,
          isFilled: this.isFilled
        };
        
        this.shapes.push(newShape);
        console.log('形状已保存:', newShape);
        
        // 标记为有未保存的更改
        this.unsavedChanges = true;
        this.addToHistory();
        
        this.showToast(`已绘制${this.getBorderTypeName()}`);
      }
      
      // 不清除绘图模式，允许连续绘制多个图形
      // 仅重置当前坐标，准备下一次绘制
      this.drawStartX = null;
      this.drawStartY = null;
      this.drawCurrentX = null;
      this.drawCurrentY = null;
      
      // 保持crosshair鼠标指针样式
      document.body.style.cursor = 'crosshair';
      
      // 移除事件监听器
      window.removeEventListener('mousemove', this.onDrawingMove);
      window.removeEventListener('mouseup', this.finishDrawing);
      
      // 阻止默认行为和事件冒泡
      event.preventDefault();
      event.stopPropagation();
    },
    
    // 框选设备
    selectDevicesInShape() {
      // 计算绘制区域
      const x1 = Math.min(this.drawStartX, this.drawCurrentX);
      const y1 = Math.min(this.drawStartY, this.drawCurrentY);
      const x2 = Math.max(this.drawStartX, this.drawCurrentX);
      const y2 = Math.max(this.drawStartY, this.drawCurrentY);
      
      console.log('框选区域:', x1, y1, x2, y2);
      console.log('设备总数:', this.canvasDevices.length);
      
      // 选中的设备
      const selectedDevices = [];
      
      // 根据形状类型选择设备
      switch (this.drawingShape) {
        case 'rectangle':
          // 矩形选择 - 检查每个设备是否在矩形内
          this.canvasDevices.forEach(device => {
            // 确保设备有宽高属性，默认为80px
            const deviceWidth = device.width || 80;
            const deviceHeight = device.height || 80;
            
            // 计算设备的中心点和边界
            const deviceLeft = device.x;
            const deviceRight = device.x + deviceWidth;
            const deviceTop = device.y;
            const deviceBottom = device.y + deviceHeight;
            const deviceCenterX = device.x + deviceWidth / 2;
            const deviceCenterY = device.y + deviceHeight / 2;
            
            console.log('设备位置:', device.id, deviceLeft, deviceTop, deviceRight, deviceBottom);
            
            // 判断设备是否与选择框相交
            const isIntersecting = !(
              deviceRight < x1 || // 设备在选择框左侧
              deviceLeft > x2 ||  // 设备在选择框右侧
              deviceBottom < y1 || // 设备在选择框上方
              deviceTop > y2      // 设备在选择框下方
            );
            
            if (isIntersecting) {
              selectedDevices.push(device);
              console.log('选中设备:', device.id);
            }
          });
          break;
          
        case 'circle':
          // 圆形选择 - 检查每个设备是否在圆内
          const centerX = (this.drawStartX + this.drawCurrentX) / 2;
          const centerY = (this.drawStartY + this.drawCurrentY) / 2;
          const radius = Math.sqrt(
            Math.pow((this.drawCurrentX - this.drawStartX) / 2, 2) + 
            Math.pow((this.drawCurrentY - this.drawStartY) / 2, 2)
          );
          
          console.log('圆形选择 - 中心点:', centerX, centerY, '半径:', radius);
          
          this.canvasDevices.forEach(device => {
            // 确保设备有宽高属性，默认为80px
            const deviceWidth = device.width || 80;
            const deviceHeight = device.height || 80;
            
            const deviceCenterX = device.x + deviceWidth / 2;
            const deviceCenterY = device.y + deviceHeight / 2;
            
            // 计算设备中心到圆心的距离
            const distance = Math.sqrt(
              Math.pow(deviceCenterX - centerX, 2) + 
              Math.pow(deviceCenterY - centerY, 2)
            );
            
            // 检查设备是否与圆相交
            // 简化判断：如果设备中心到圆心的距离小于(半径+设备对角线/2)，则认为相交
            const deviceDiagonal = Math.sqrt(Math.pow(deviceWidth, 2) + Math.pow(deviceHeight, 2)) / 2;
            
            if (distance <= radius + deviceDiagonal) {
              selectedDevices.push(device);
              console.log('圆形选择 - 选中设备:', device.id);
            }
          });
          break;
          
        case 'line':
          // 线条选择 - 检查每个设备是否接近线条
          console.log('线条选择 - 起点:', this.drawStartX, this.drawStartY, '终点:', this.drawCurrentX, this.drawCurrentY);
          
          this.canvasDevices.forEach(device => {
            // 确保设备有宽高属性，默认为80px
            const deviceWidth = device.width || 80;
            const deviceHeight = device.height || 80;
            
            const deviceCenterX = device.x + deviceWidth / 2;
            const deviceCenterY = device.y + deviceHeight / 2;
            
            // 计算设备到线的距离
            const distance = this.pointToLineDistance(
              deviceCenterX, deviceCenterY,
              this.drawStartX, this.drawStartY,
              this.drawCurrentX, this.drawCurrentY
            );
            
            console.log('设备:', device.id, '到线的距离:', distance);
            
            // 如果距离小于阈值，则选中设备
            // 考虑设备大小，增大阈值
            const threshold = Math.min(deviceWidth, deviceHeight) / 2 + 20; // 基础阈值20像素加上设备尺寸的一半
            
            if (distance <= threshold) {
              selectedDevices.push(device);
              console.log('线条选择 - 选中设备:', device.id);
            }
          });
          break;
      }
      
      // 如果有选中的设备
      if (selectedDevices.length > 0) {
        // 如果只有一个设备，选中它
        if (selectedDevices.length === 1) {
          this.selectedItem = selectedDevices[0];
          this.showToast('已选中 1 个设备');
        } else {
          // 多个设备，应用批量样式
          selectedDevices.forEach(device => {
            // 应用样式
            device.borderColor = this.currentColor;
            device.borderWidth = this.borderWidth;
            device.borderStyle = this.selectedBorderStyle;
            if (this.isFilled) {
              device.fillColor = this.currentColor;
            } else {
              device.fillColor = 'transparent';
            }
          });
          
          // 标记为有未保存的更改
          this.unsavedChanges = true;
          this.addToHistory();
          
          this.showToast(`已应用样式到 ${selectedDevices.length} 个设备`);
        }
      } else {
        this.showToast('未选中任何设备');
      }
    },
    
    // 计算点到线的距离
    pointToLineDistance(px, py, x1, y1, x2, y2) {
      // 线段长度的平方
      const lineLength2 = Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2);
      
      if (lineLength2 === 0) {
        // 如果线段长度为0，则返回点到端点的距离
        return Math.sqrt(Math.pow(px - x1, 2) + Math.pow(py - y1, 2));
      }
      
      // 计算投影比例
      const t = ((px - x1) * (x2 - x1) + (py - y1) * (y2 - y1)) / lineLength2;
      
      if (t < 0) {
        // 投影点在线段外部，靠近起点
        return Math.sqrt(Math.pow(px - x1, 2) + Math.pow(py - y1, 2));
      } else if (t > 1) {
        // 投影点在线段外部，靠近终点
        return Math.sqrt(Math.pow(px - x2, 2) + Math.pow(py - y2, 2));
      } else {
        // 投影点在线段内部
        const projX = x1 + t * (x2 - x1);
        const projY = y1 + t * (y2 - y1);
        return Math.sqrt(Math.pow(px - projX, 2) + Math.pow(py - projY, 2));
      }
    },
    
    // 显示确认对话框
    showConfirm(message, action) {
      this.confirmDialogMessage = message;
      this.confirmDialogAction = action;
      this.showConfirmDialog = true;
    },
    
    // 确认对话框确认操作
    confirmAction() {
      // 执行存储的回调操作
      if (this.confirmDialogAction) {
        this.confirmDialogAction();
      }
      
      // 关闭确认对话框
      this.showConfirmDialog = false;
      this.confirmDialogAction = null;
    },
    
    // 取消确认对话框
    cancelConfirm() {
      this.showConfirmDialog = false;
      this.confirmDialogAction = null;
    },
    
    // 新建拓扑图
    createNewTopology() {
      // 如果有未保存的更改，显示确认对话框
      if (this.unsavedChanges) {
        this.showConfirm('当前拓扑图有未保存的更改，确定要创建新拓扑图吗？', this.doCreateNewTopology);
      } else {
        this.doCreateNewTopology();
      }
    },
    
    // 实际创建新拓扑图的逻辑
    doCreateNewTopology() {
      // 清空拓扑图
      this.canvasDevices = [];
      this.connections = [];
      
      // 重置文件名
      this.topologyFileName = '';
      
      // 重置历史记录
      this.history = [];
      this.historyIndex = -1;
      
      // 添加初始历史记录
      this.addToHistory();
      
      // 重置选中状态
      this.selectedItem = null;
      
      // 标记为未保存状态已重置
      this.unsavedChanges = false;
      
      // 显示提示
      this.showToast('已创建新拓扑图');
    },
    
    // 将拓扑图保存到localStorage
    saveTopologyToStorage() {
      // 检查文件名是否有效
      if (!this.topologyFileName.trim()) {
        alert('请输入有效的拓扑图名称');
        return false;
      }
      
      // 创建要保存的拓扑数据对象
      const topologyData = {
        name: this.topologyFileName,
        date: new Date().toISOString(),
        devices: this.canvasDevices,
        connections: this.connections,
        shapes: this.shapes,
        gridEnabled: this.gridEnabled,
        gridSize: this.gridSize,
        pathStyle: this.pathStyle,
        version: '1.0'
      };
      
      // 获取现有保存的拓扑图列表
      let savedTopologies = JSON.parse(localStorage.getItem('savedTopologies') || '[]');
      
      // 检查是否已存在相同名称的拓扑图
      const existingIndex = savedTopologies.findIndex(t => t.name === this.topologyFileName);
      if (existingIndex !== -1) {
        // 如果存在，更新现有拓扑图
        savedTopologies[existingIndex] = topologyData;
      } else {
        // 如果不存在，添加新拓扑图
        savedTopologies.push(topologyData);
      }
      
      // 保存回localStorage
      try {
        localStorage.setItem('savedTopologies', JSON.stringify(savedTopologies));
        // 标记为已保存
        this.unsavedChanges = false;
        console.log('拓扑已保存到本地存储:', this.topologyFileName);
        
        // 刷新保存的拓扑图列表
        this.loadSavedTopologyList();
        return true;
      } catch (e) {
        console.error('保存拓扑图失败:', e);
        alert('保存拓扑图失败，可能是存储空间不足。');
        return false;
      }
    },
    
    // 确认保存拓扑图到localStorage (从对话框调用)
    confirmSaveTopology() {
      // 检查文件名是否有效
      if (!this.topologyFileName.trim()) {
        alert('请输入有效的拓扑图名称');
        return;
      }
      
      // 使用公共方法保存拓扑图
      const saved = this.saveTopologyToStorage();
      
      if (saved) {
        // 关闭保存对话框
        this.showSaveDialog = false;
      }
    },
    
    // 加载保存的拓扑图列表
    loadSavedTopologyList() {
      try {
        const savedTopologies = JSON.parse(localStorage.getItem('savedTopologies') || '[]');
        this.savedTopologies = savedTopologies;
      } catch (e) {
        console.error('加载拓扑图列表失败:', e);
        this.savedTopologies = [];
      }
    },
    
    // 导出拓扑图为JSON文件
    exportTopologyAsJson() {
      // 创建要保存的拓扑数据对象
      const topologyData = {
        devices: this.canvasDevices,
        connections: this.connections,
        shapes: this.shapes,
        gridEnabled: this.gridEnabled,
        gridSize: this.gridSize,
        pathStyle: this.pathStyle,
        version: '1.0'
      };
      
      // 转换为JSON字符串
      const jsonData = JSON.stringify(topologyData, null, 2);
      
      // 创建Blob对象
      const blob = new Blob([jsonData], { type: 'application/json' });
      
      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // 设置文件名
      const fileName = this.topologyFileName || `topology-${new Date().toISOString().slice(0, 10)}.json`;
      link.download = fileName + '.json';
      
      // 触发下载
      document.body.appendChild(link);
      link.click();
      
      // 清理
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      console.log('拓扑已导出为JSON文件:', fileName);
    },
    
    // 显示导出连线表对话框
    exportConnectionTable() {
      this.showExportDialog = true;
      this.exportFormat = 'csv'; // 默认选择CSV格式
    },
    
    // 导出连线表为CSV文件
    exportConnectionsToCSV() {
      if (this.connections.length === 0) {
        this.showToast('没有连线数据可导出');
        return;
      }
      
      // 准备CSV表头
      let csvContent = '本端设备,本端端口,对端设备,对端端口\n';
      
      // 遍历所有连接，生成CSV行
      this.connections.forEach(connection => {
        // 获取源设备和目标设备
        const sourceDevice = this.canvasDevices.find(device => device.id === connection.source);
        const targetDevice = this.canvasDevices.find(device => device.id === connection.target);
        
        if (sourceDevice && targetDevice) {
          // 添加连接信息到CSV
          const sourceName = sourceDevice.name || '未命名设备';
          const sourcePort = connection.sourcePort || '未指定';
          const targetName = targetDevice.name || '未命名设备';
          const targetPort = connection.targetPort || '未指定';
          
          // 转义CSV中的逗号
          const escapeCsv = (text) => {
            if (text.includes(',')) {
              return `"${text}"`;
            }
            return text;
          };
          
          csvContent += `${escapeCsv(sourceName)},${escapeCsv(sourcePort)},${escapeCsv(targetName)},${escapeCsv(targetPort)}\n`;
        }
      });
      
      // 创建Blob对象
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      
      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // 设置文件名
      const fileName = (this.topologyFileName || `topology-${new Date().toISOString().slice(0, 10)}`) + '-connections.csv';
      link.download = fileName;
      
      // 触发下载
      document.body.appendChild(link);
      link.click();
      
      // 清理
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      this.showToast('连线表已导出为CSV文件');
      console.log('连线表已导出为CSV文件:', fileName);
    },
    
    // 导出连线表为Excel文件
    async exportConnectionsToExcel() {
      if (this.connections.length === 0) {
        this.showToast('没有连线数据可导出');
        return;
      }
      
      try {
        // 动态导入xlsx库
        const XLSX = await import('xlsx-js-style');
        
        // 准备连线数据
        const connectionData = [];
        
        // 添加表头
        connectionData.push(['本端设备', '本端端口', '对端设备', '对端端口']);
        
        // 遍历所有连接，添加数据行
        this.connections.forEach(connection => {
          // 获取源设备和目标设备
          const sourceDevice = this.canvasDevices.find(device => device.id === connection.source);
          const targetDevice = this.canvasDevices.find(device => device.id === connection.target);
          
          if (sourceDevice && targetDevice) {
            // 添加连接信息
            const sourceName = sourceDevice.name || '未命名设备';
            const sourcePort = connection.sourcePort || '未指定';
            const targetName = targetDevice.name || '未命名设备';
            const targetPort = connection.targetPort || '未指定';
            
            connectionData.push([sourceName, sourcePort, targetName, targetPort]);
          }
        });
        
        // 创建工作簿
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.aoa_to_sheet(connectionData);
        
        // 添加工作表到工作簿
        XLSX.utils.book_append_sheet(wb, ws, '连线表');
        
        // 设置文件名
        const fileName = (this.topologyFileName || `topology-${new Date().toISOString().slice(0, 10)}`) + '-connections.xlsx';
        
        // 导出Excel文件
        XLSX.writeFile(wb, fileName);
        
        this.showToast('连线表已导出为Excel文件');
        console.log('连线表已导出为Excel文件:', fileName);
      } catch (error) {
        console.error('导出Excel文件失败:', error);
        this.showToast('导出Excel文件失败，请尝试CSV格式');
      }
    },
    
    // 显示打开拓扑图对话框
    uploadTopology() {
      // 如果有未保存的更改，显示确认对话框
      if (this.unsavedChanges) {
        this.showConfirm('当前拓扑图有未保存的更改，确定要打开其他拓扑图吗？', this.doShowOpenDialog);
      } else {
        this.doShowOpenDialog();
      }
    },
    
    // 实际显示打开对话框的逻辑
    doShowOpenDialog() {
      // 加载保存的拓扑图列表
      this.loadSavedTopologyList();
      
      // 显示打开对话框
      this.showOpenDialog = true;
    },
    
    // 从localStorage加载拓扑图
    loadSavedTopology(savedTopology) {
      try {
        // 验证数据格式
        if (!savedTopology.devices || !savedTopology.connections) {
          throw new Error('无效的拓扑文件格式');
        }
        
        // 保存文件名
        this.topologyFileName = savedTopology.name;
        
        // 加载拓扑数据
        this.canvasDevices = savedTopology.devices;
        this.connections = savedTopology.connections;
        
        // 加载形状数据（如果有）
        if (savedTopology.shapes) {
          this.shapes = savedTopology.shapes;
        } else {
          this.shapes = []; // 如果没有形状数据，初始化为空数组
        }
        
        // 加载设置
        if (savedTopology.gridEnabled !== undefined) this.gridEnabled = savedTopology.gridEnabled;
        if (savedTopology.gridSize !== undefined) this.gridSize = savedTopology.gridSize;
        if (savedTopology.pathStyle !== undefined) this.pathStyle = savedTopology.pathStyle;
        
        // 清除选中状态
        this.selectedItem = null;
        
        // 重置历史记录
        this.history = [];
        this.historyIndex = -1;
        
        // 标记为已保存
        this.unsavedChanges = false;
        
        // 关闭打开对话框
        this.showOpenDialog = false;
        
        console.log('拓扑已加载:', this.topologyFileName);
      } catch (error) {
        console.error('加载拓扑文件失败:', error);
        alert('加载拓扑文件失败: ' + error.message);
      }
    },
    
    // 删除已保存的拓扑图
    deleteSavedTopology(topologyName) {
      // 确认删除
      if (!confirm(`确定要删除拓扑图 "${topologyName}" 吗？`)) {
        return;
      }
      
      // 获取现有保存的拓扑图列表
      let savedTopologies = JSON.parse(localStorage.getItem('savedTopologies') || '[]');
      
      // 移除指定名称的拓扑图
      savedTopologies = savedTopologies.filter(t => t.name !== topologyName);
      
      // 保存回localStorage
      localStorage.setItem('savedTopologies', JSON.stringify(savedTopologies));
      
      // 刷新保存的拓扑图列表
      this.loadSavedTopologyList();
      
      console.log('已删除拓扑图:', topologyName);
    },
    
    // 导入JSON文件
    importTopologyFromJson() {
      // 如果有未保存的更改，显示确认对话框
      if (this.unsavedChanges) {
        this.showConfirm('当前拓扑图有未保存的更改，确定要导入新的拓扑图吗？', () => {
          this.doImportTopologyFromJson();
        });
      } else {
        this.doImportTopologyFromJson();
      }
    },
    
    // 实际导入JSON文件的逻辑
    doImportTopologyFromJson() {
      // 创建文件输入元素
      const fileInput = document.createElement('input');
      fileInput.type = 'file';
      fileInput.accept = '.json';
      
      // 监听文件选择事件
      fileInput.addEventListener('change', (event) => {
        const file = event.target.files[0];
        if (!file) return;
        
        // 提取文件名（去除.json扩展名）
        const fileName = file.name.replace(/\.json$/, '');
        this.topologyFileName = fileName;
        
        // 读取文件内容
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const data = JSON.parse(e.target.result);
            
            // 验证数据格式
            if (!data.devices || !data.connections) {
              throw new Error('无效的拓扑文件格式');
            }
            
            // 加载拓扑数据
            this.canvasDevices = data.devices;
            this.connections = data.connections;
            
            // 加载形状数据（如果有）
            if (data.shapes) {
              this.shapes = data.shapes;
            } else {
              this.shapes = []; // 如果没有形状数据，初始化为空数组
            }
            
            // 加载设置
            if (data.gridEnabled !== undefined) this.gridEnabled = data.gridEnabled;
            if (data.gridSize !== undefined) this.gridSize = data.gridSize;
            if (data.pathStyle !== undefined) this.pathStyle = data.pathStyle;
            
            // 清除选中状态
            this.selectedItem = null;
            
            // 重置历史记录
            this.history = [];
            this.historyIndex = -1;
            
            // 关闭对话框
            this.showOpenDialog = false;
            
            console.log('JSON拓扑文件已导入:', fileName);
          } catch (error) {
            console.error('导入拓扑文件失败:', error);
            alert('导入拓扑文件失败: ' + error.message);
          }
        };
        
        reader.readAsText(file);
      });
      
      // 触发文件选择对话框
      fileInput.click();
    },
    
    // 添加历史记录功能
    addToHistory() {
      // 创建当前状态的快照
      const snapshot = {
        devices: JSON.parse(JSON.stringify(this.canvasDevices)),
        connections: JSON.parse(JSON.stringify(this.connections)),
        shapes: JSON.parse(JSON.stringify(this.shapes))
      };
      
      // 如果当前不在历史记录的最新点，删除后面的历史记录
      if (this.historyIndex < this.history.length - 1) {
        this.history = this.history.slice(0, this.historyIndex + 1);
      }
      
      // 添加新的历史记录
      this.history.push(snapshot);
      
      // 如果历史记录超出最大长度，删除最早的记录
      if (this.history.length > this.maxHistoryLength) {
        this.history.shift();
      } else {
        this.historyIndex++;
      }
      
      // 标记为未保存
      this.unsavedChanges = true;
    },
    
    // 撤销操作
    undo() {
      if (this.historyIndex <= 0) {
        console.log('没有可撤销的操作');
        return;
      }
      
      // 减少历史索引
      this.historyIndex--;
      
      // 恢复到历史状态
      const snapshot = this.history[this.historyIndex];
      this.canvasDevices = JSON.parse(JSON.stringify(snapshot.devices));
      this.connections = JSON.parse(JSON.stringify(snapshot.connections));
      this.shapes = snapshot.shapes ? JSON.parse(JSON.stringify(snapshot.shapes)) : [];
      
      // 清除选中状态
      this.selectedItem = null;
      
      console.log('撤销操作完成');
    },
    
    // 重做操作
    redo() {
      if (this.historyIndex >= this.history.length - 1) {
        console.log('没有可重做的操作');
        return;
      }
      
      // 增加历史索引
      this.historyIndex++;
      
      // 恢复到历史状态
      const snapshot = this.history[this.historyIndex];
      this.canvasDevices = JSON.parse(JSON.stringify(snapshot.devices));
      this.connections = JSON.parse(JSON.stringify(snapshot.connections));
      this.shapes = snapshot.shapes ? JSON.parse(JSON.stringify(snapshot.shapes)) : [];
      
      // 清除选中状态
      this.selectedItem = null;
      
      console.log('重做操作完成');
    },
    
          // 导出连线表对话框确认操作
      confirmExport() {
        // 根据选择的格式导出连线表
        if (this.exportFormat === 'csv') {
          this.exportConnectionsToCSV();
        } else if (this.exportFormat === 'excel') {
          this.exportConnectionsToExcel();
        }
        
        // 关闭导出对话框
        this.showExportDialog = false;
      },
    
    // 获取设备名称
    getDeviceName(device) {
      const foundDevice = this.canvasDevices.find(d => d.id === device);
      return foundDevice ? foundDevice.name : '未知设备';
    },
    
    // 过滤和排序连线
    filterConnections() {
      // 先应用搜索过滤
      let result = [...this.connections];
      
      if (this.connectionFilter.search) {
        const searchTerm = this.connectionFilter.search.toLowerCase();
        result = result.filter(conn => {
          const sourceDevice = this.canvasDevices.find(d => d.id === conn.source);
          const targetDevice = this.canvasDevices.find(d => d.id === conn.target);
          
          // 检查源设备名称、目标设备名称、源端口和目标端口
          return (
            (sourceDevice && sourceDevice.name && sourceDevice.name.toLowerCase().includes(searchTerm)) ||
            (targetDevice && targetDevice.name && targetDevice.name.toLowerCase().includes(searchTerm)) ||
            (conn.sourcePort && conn.sourcePort.toLowerCase().includes(searchTerm)) ||
            (conn.targetPort && conn.targetPort.toLowerCase().includes(searchTerm))
          );
        });
      }
      
      // 再排序
      result.sort((a, b) => {
        let valueA, valueB;
        
        // 根据排序字段获取值
        switch (this.connectionFilter.sortBy) {
          case 'source':
            valueA = this.getDeviceName(a.source).toLowerCase();
            valueB = this.getDeviceName(b.source).toLowerCase();
            break;
          case 'target':
            valueA = this.getDeviceName(a.target).toLowerCase();
            valueB = this.getDeviceName(b.target).toLowerCase();
            break;
          case 'sourcePort':
            valueA = (a.sourcePort || '').toLowerCase();
            valueB = (b.sourcePort || '').toLowerCase();
            break;
          case 'targetPort':
            valueA = (a.targetPort || '').toLowerCase();
            valueB = (b.targetPort || '').toLowerCase();
            break;
          default:
            valueA = this.getDeviceName(a.source).toLowerCase();
            valueB = this.getDeviceName(b.source).toLowerCase();
        }
        
        // 根据排序方向返回比较结果
        if (this.connectionFilter.sortAsc) {
          return valueA.localeCompare(valueB);
        } else {
          return valueB.localeCompare(valueA);
        }
      });
      
      this.filteredConnections = result;
    },
    
    // 设置排序字段
    setSortBy(field) {
      if (this.connectionFilter.sortBy === field) {
        // 如果已经按此字段排序，则切换排序方向
        this.connectionFilter.sortAsc = !this.connectionFilter.sortAsc;
      } else {
        // 否则，设置新的排序字段并默认为升序
        this.connectionFilter.sortBy = field;
        this.connectionFilter.sortAsc = true;
      }
      
      this.filterConnections();
    },
    
    // 切换排序方向
    toggleSortDirection() {
      this.connectionFilter.sortAsc = !this.connectionFilter.sortAsc;
      this.filterConnections();
    },
    
    // 从列表中选择连线
    selectConnectionFromList(connection) {
      this.selectedItem = connection;
    },
    
    // 定位到图形中的连线
    locateConnection(connection) {
      // 先选中连线
      this.selectedItem = connection;
      
      // 获取连线的中间点
      const midX = (connection.sourceX + connection.targetX) / 2;
      const midY = (connection.sourceY + connection.targetY) / 2;
      
      // 添加高亮闪烁效果
      this.$nextTick(() => {
        // 查找连线元素
        const connectionPath = document.querySelector(`.connection-path[data-id="${connection.id}"]`);
        if (connectionPath) {
          // 添加高亮动画类
          connectionPath.classList.add('highlight-pulse');
          
          // 3秒后移除高亮效果
          setTimeout(() => {
            connectionPath.classList.remove('highlight-pulse');
          }, 3000);
        }
      });
      
      // 显示提示
      this.showToast('已定位到连线');
    },
    
    // 开始调整面板大小
    startResizingPanel(event) {
      this.isResizingPanel = true;
      this.panelResizeStartY = event.clientY;
      this.panelResizeStartHeight = this.connectionListHeight;
      
      // 添加全局事件监听
      document.addEventListener('mousemove', this.resizePanel);
      document.addEventListener('mouseup', this.stopResizingPanel);
      
      // 阻止默认行为和事件传播
      event.preventDefault();
      event.stopPropagation();
    },
    
    // 调整面板大小
    resizePanel(event) {
      if (!this.isResizingPanel) return;
      
      // 计算高度差
      const deltaY = this.panelResizeStartY - event.clientY;
      let newHeight = this.panelResizeStartHeight + deltaY;
      
      // 限制最小高度和最大高度
      newHeight = Math.max(180, Math.min(500, newHeight));
      
      // 更新面板高度
      this.connectionListHeight = newHeight;
      
      // 阻止默认行为
      event.preventDefault();
    },
    
    // 停止调整面板大小
    stopResizingPanel() {
      this.isResizingPanel = false;
      
      // 移除全局事件监听
      document.removeEventListener('mousemove', this.resizePanel);
      document.removeEventListener('mouseup', this.stopResizingPanel);
    },
  },
  created() {
    // 初始化时设置默认显示的设备为路由设备
    this.devices = this.routerDevices;
    
    // 初始化历史记录
    this.addToHistory();
    
    // 加载保存的拓扑图列表
    this.loadSavedTopologyList();
  },
  
  mounted() {
    // 确保设备可以在画布中拖动
    document.addEventListener('mouseup', this.stopDrag);
    
    // 应用初始缩放
    this.updateCanvasZoom();
    
    // 添加键盘快捷键监听
    window.addEventListener('keydown', this.handleKeyDown);
    
    console.log('已添加键盘事件监听器，按Escape键可退出编辑模式');
    
    // 初始化过滤后的连接列表
    this.filterConnections();
  },
  
  beforeUnmount() {
    // 清除全局事件监听器
    document.removeEventListener('mouseup', this.stopDrag);
    window.removeEventListener('mousemove', this.onDrag);
    window.removeEventListener('keydown', this.handleKeyDown);
  },
  // 添加键盘快捷键处理方法
  handleKeyDown(event) {
    // Ctrl+Z: 撤销
    if ((event.ctrlKey || event.metaKey) && event.key === 'z' && !event.shiftKey) {
      event.preventDefault();
      this.undo();
    }
    
    // Ctrl+Shift+Z 或 Ctrl+Y: 重做
    if ((event.ctrlKey || event.metaKey) && ((event.key === 'z' && event.shiftKey) || event.key === 'y')) {
      event.preventDefault();
      this.redo();
    }
    
    // Ctrl+S: 保存
    if ((event.ctrlKey || event.metaKey) && event.key === 's') {
      event.preventDefault();
      this.saveTopology();
    }
  },
  watch: {
    // 监听网格大小变化
    gridSize(newSize) {
      console.log('网格大小已更新为:', newSize + 'px');
    },
    
    // 监听画布内容变化
    canvasDevices: {
      handler() {
        this.unsavedChanges = true;
      },
      deep: true
    },
    
    connections: {
      handler() {
        this.unsavedChanges = true;
        this.filterConnections();
      },
      deep: true
    },
    
    shapes: {
      handler() {
        this.unsavedChanges = true;
      },
      deep: true
    }
  }
}
</script>

<style scoped>
.topology-container {
  display: flex;
  width: 100%;
  height: 100vh;
  background-color: #f5f5f5;
  color: #333;
  overflow: hidden;
  --primary-color: #1890ff;
  --secondary-color: #096dd9;
  --hover-color: rgba(24, 144, 255, 0.1);
  --active-color: rgba(24, 144, 255, 0.15);
  --icon-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  --icon-shadow-hover: 0 3px 6px rgba(24, 144, 255, 0.3);
}

/* 对话框样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  width: 400px;
  max-width: 90%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

/* 确认对话框特有样式 */
.confirm-dialog {
  width: 350px;
}

.confirm-dialog p {
  margin: 0;
  font-size: 15px;
  line-height: 1.6;
}

/* 调色板对话框样式 */
.palette-dialog {
  width: 420px;
}

.palette-dialog .dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
}

.palette-row {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

/* 边框类型选择 */
.border-type-option {
  width: 80px;
  height: 60px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-right: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  background-color: #f9f9f9;
}

.border-type-option:hover {
  background-color: #f0f0f0;
}

.border-type-option.active {
  border-color: #1890ff;
  background-color: #e6f7ff;
}

.border-type-icon {
  width: 50px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.line-icon {
  width: 30px;
  height: 2px;
  background-color: #666;
}

.rectangle-icon {
  width: 30px;
  height: 20px;
  border: 2px solid #666;
}

.circle-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid #666;
}

/* 样式选择 */
.palette-label {
  width: 80px;
  font-size: 14px;
}

.palette-input {
  flex: 1;
}

.style-select {
  width: 100%;
  padding: 6px 10px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

/* 颜色选择器 */
.color-preview {
  width: 36px;
  height: 36px;
  border-radius: 4px;
  border: 1px solid #ddd;
  cursor: pointer;
  margin-right: 20px;
}

/* 填充选项 */
.fill-options {
  display: flex;
  align-items: center;
}

.fill-option {
  margin-right: 15px;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.fill-option input {
  margin-right: 5px;
}

.dialog-header {
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.dialog-header h3 {
  margin: 0;
  font-size: 18px;
}

.dialog-body {
  padding: 16px;
  overflow-y: auto;
  max-height: 60vh;
}

.dialog-footer {
  padding: 12px 16px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
}

.form-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

/* 按钮样式 */
.cancel-btn,
.confirm-btn,
.export-btn,
.open-btn,
.delete-btn,
.import-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.cancel-btn {
  background-color: #f0f0f0;
}

.confirm-btn {
  background-color: #1890ff;
  color: white;
}

.export-btn {
  background-color: #52c41a;
  color: white;
}

.open-btn {
  background-color: #1890ff;
  color: white;
  padding: 4px 12px;
}

.delete-btn {
  background-color: #ff4d4f;
  color: white;
  padding: 4px 12px;
}

.import-btn {
  background-color: #722ed1;
  color: white;
  margin-top: 16px;
  width: 100%;
}

/* 保存的拓扑图列表样式 */
.saved-topologies-list {
  margin-bottom: 16px;
}

.saved-topology-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-bottom: 8px;
  transition: background-color 0.2s;
}

.saved-topology-item:hover {
  background-color: #f9f9f9;
}

.saved-topology-info {
  flex: 1;
}

.saved-topology-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.saved-topology-date {
  font-size: 12px;
  color: #999;
}

.saved-topology-actions {
  display: flex;
  gap: 8px;
}

.no-saved-topologies {
  text-align: center;
  padding: 24px 0;
  color: #999;
}

.import-option {
  border-top: 1px solid #eee;
  padding-top: 16px;
  margin-top: 16px;
}

/* 左侧设备库样式 */
.sidebar {
  width: 250px;
  border-right: 1px solid #ddd;
  background-color: white;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-header {
  padding: 16px;
  font-size: 18px;
  font-weight: bold;
  border-bottom: 1px solid #eee;
}

.search-box {
  padding: 8px;
  border-bottom: 1px solid #eee;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.tab-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.tabs {
  display: flex;
  flex-direction: column;
  border-bottom: 1px solid #eee;
}

.tabs-row {
  display: flex;
  width: 100%;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 10px 0;
  cursor: pointer;
}

.tab.active {
  border-bottom: 2px solid #1890ff;
  color: #1890ff;
}

.device-list {
  flex: 1;
  overflow-y: auto;
}

.device-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: background-color 0.2s;
}

.device-item:hover {
  background-color: #f0f0f0;
}

.device-icon {
  width: 40px;
  height: 40px;
  background-color: #f0f7ff;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.device-svg {
  width: 24px;
  height: 24px;
  fill: #1890ff;
}

.device-info {
  flex: 1;
}

.device-name {
  font-size: 14px;
  margin-bottom: 2px;
}

.device-type {
  font-size: 12px;
  color: #999;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  overflow: hidden;
  position: relative;
}

/* 顶部工具栏样式 */
.top-toolbar {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background-color: white;
  border-bottom: 1px solid #ddd;
  height: 48px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.05);
}

.toolbar-group {
  display: flex;
  align-items: center;
}

.toolbar-divider-vertical {
  width: 1px;
  height: 24px;
  background-color: #ddd;
  margin: 0 12px;
}

.toolbar-item {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  cursor: pointer;
  color: #666;
  margin-right: 4px;
  transition: all 0.2s;
}

.toolbar-item:last-child {
  margin-right: 0;
}

.toolbar-item:hover {
  background-color: #f0f0f0;
  color: #1890ff;
}

.toolbar-item.active {
  background-color: #e6f7ff;
  color: #1890ff;
}

/* 中间拓扑图区域样式 */
.topology-canvas {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f0f2f5;
  position: relative;
  overflow: hidden;
}

.canvas-header {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  background-color: white;
  border-bottom: 1px solid #eee;
}

.header-title {
  font-weight: bold;
  margin-right: 10px;
}

.header-subtitle {
  font-size: 12px;
  color: #999;
}

.canvas-content {
  flex: 1;
  background-color: white;
  position: relative;
  overflow: auto; /* Changed from 'hidden' to 'auto' to allow scrolling */
  width: 100%;
  height: 100%;
  min-height: 600px; /* Ensure minimum height for small screens */
  transition: transform 0.2s ease; /* Add smooth transition for zoom */
  transform-origin: center center; /* Set transform origin to center */
}

.drawing-mode {
  cursor: crosshair !important;
}

.shape-element {
  cursor: pointer;
  transition: all 0.2s;
  pointer-events: all !important;
  z-index: 20 !important;
  position: relative;
}

.shape-element:hover {
  stroke-opacity: 0.8;
  filter: drop-shadow(0px 0px 3px rgba(0, 0, 0, 0.3));
}

/* Add grid display based on gridEnabled property */
.canvas-content.grid-enabled {
  background-image: 
    linear-gradient(to right, rgba(224, 224, 224, 0.5) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(224, 224, 224, 0.5) 1px, transparent 1px);
  background-size: 20px 20px;
}

.toolbar-item {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  cursor: pointer;
  color: #666;
  margin-right: 4px;
  transition: all 0.2s;
}

.toolbar-item:last-child {
  margin-right: 0;
}

.toolbar-item:hover {
  background-color: #f0f0f0;
  color: #1890ff;
}

.toolbar-item.active {
  background-color: #e6f7ff;
  color: #1890ff;
}

.empty-canvas {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.empty-text {
  font-size: 16px;
  color: #999;
  margin-top: 8px;
}

.empty-subtext {
  font-size: 14px;
  color: #bbb;
  margin-top: 4px;
}

.canvas-footer {
  padding: 6px 16px;
  background-color: white;
  border-top: 1px solid #eee;
  display: flex;
  font-size: 12px;
  color: #666;
}

.footer-info {
  margin-right: 16px;
}

.canvas-controls {
  margin-left: auto;
}

.canvas-controls span {
  margin-left: 12px;
}

/* 右侧属性面板样式 */
.properties-panel {
  width: 250px;
  border-left: 1px solid #ddd;
  background-color: white;
}

.panel-header {
  padding: 16px;
  font-size: 16px;
  font-weight: bold;
  border-bottom: 1px solid #eee;
}

.panel-content {
  padding: 16px;
}

.panel-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #999;
  text-align: center;
}

.property-section {
  margin-bottom: 16px;
}

.section-title {
  font-weight: bold;
  margin-bottom: 12px;
}

.property-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.property-label {
  font-size: 14px;
}

.property-select {
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 120px;
}

/* 开关样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #1890ff;
}

input:checked + .slider:before {
  transform: translateX(20px);
}

input[type=range].slider {
  width: 100%;
  margin: 0;
  background-color: transparent;
  max-width: 120px;
}

/* 画布上的设备样式 */
.canvas-device {
  position: absolute;
  width: 80px;
  height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  user-select: none;
  z-index: 10;
  transition: transform 0.1s;
}

.canvas-device:hover {
  transform: scale(1.05);
}

.canvas-device-icon {
  width: 50px;
  height: 50px;
  background-color: #f0f7ff;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.canvas-device-label {
  position: absolute;
  margin-top: 4px;
  font-size: 12px;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 4px 8px;
  border-radius: 4px;
  min-width: 80px;
  max-width: 180px;
  overflow: visible;
  text-align: center;
  cursor: move;
  z-index: 11;
  box-shadow: 0 2px 4px rgba(0,0,0,0.15);
  top: 60px; /* 默认位置在设备下方 */
  left: 50%;
  transform: translateX(-50%);
  word-break: break-word;
  line-height: 1.4;
}

/* 连接点样式 - 禁用 */
.connection-points {
  display: none;
}

.connection-point {
  display: none;
}

/* 连接线样式 */
.connection-path {
  fill: none;
  /* 基本样式由动态样式控制 */
  stroke-linecap: round;
  stroke-linejoin: round;
  pointer-events: all;
  cursor: pointer;
}

.selected {
  stroke: #ffd700 !important;
  stroke-width: 3px !important;
}

.new-connection {
  stroke: #ff4d4f;
  stroke-dasharray: 5, 5;
  stroke-width: 2px;
  animation: dash 1s linear infinite;
}

@keyframes dash {
  to {
    stroke-dashoffset: -10;
  }
}

/* 连接标签样式 */
.connection-label {
  fill: #1890ff;
  font-size: 12px;
  text-anchor: middle;
  dominant-baseline: middle;
  user-select: none;
}

.property-value {
  max-width: 120px;
  overflow: hidden;
}

.property-input.color-picker {
  width: 40px;
  height: 30px;
  padding: 0;
  border: 1px solid #ddd;
  cursor: pointer;
}

.property-textarea {
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  resize: vertical;
  width: 100%;
  min-height: 60px;
}

/* 端口选择对话框样式 */
.port-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.port-dialog-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  width: 350px;
}

.port-dialog-header {
  margin-bottom: 16px;
}

.port-dialog-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
}

.dialog-device-info {
  font-size: 14px;
  color: #666;
}

.port-dialog-body {
  margin-bottom: 20px;
}

.port-input-container {
  display: flex;
  flex-direction: column;
}

.port-input-container label {
  margin-bottom: 8px;
  font-size: 14px;
}

.port-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.port-dialog-footer {
  display: flex;
  justify-content: flex-end;
}

button.cancel-btn, 
button.confirm-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

button.cancel-btn {
  background-color: #f0f0f0;
  margin-right: 8px;
}

button.confirm-btn {
  background-color: #1890ff;
  color: white;
}

button.cancel-btn:hover {
  background-color: #e0e0e0;
}

button.confirm-btn:hover {
  background-color: #40a9ff;
}

/* 端口标签样式 */
.port-label {
  fill: #1890ff;
  font-size: 12px;
  font-weight: bold;
  text-anchor: middle;
  dominant-baseline: middle;
  user-select: none;
}

.port-label-box {
  fill: white;
  stroke: none; /* Removed border */
  rx: 5px;
  ry: 5px;
  filter: drop-shadow(0px 1px 3px rgba(0,0,0,0.1));
}

.port-generator {
  margin-bottom: 20px;
}

.port-generator-header {
  font-weight: bold;
  margin-bottom: 10px;
}

.port-generator-content {
  display: flex;
  flex-direction: column;
}

.port-generator-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.port-generator-label {
  font-size: 14px;
}

.port-generator-select {
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 120px;
}

.port-generator-result {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.port-generator-value {
  font-size: 14px;
}

.port-generator-use-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  background-color: #1890ff;
  color: white;
}

.port-generator-use-btn:hover {
  background-color: #40a9ff;
}

/* 添加状态指示器样式 */
.unsaved-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #ff4d4f;
  margin-right: 6px;
}

/* 添加按钮禁用状态 */
.toolbar-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-item.disabled:hover {
  background-color: transparent;
  color: #666;
}

/* 提示消息样式 */
.toast-message {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 1100;
  animation: fadeInOut 3s ease-in-out;
}

@keyframes fadeInOut {
  0% { opacity: 0; }
  10% { opacity: 1; }
  80% { opacity: 1; }
  100% { opacity: 0; }
}

/* 添加选中和编辑模式样式 */
.shape-element {
  pointer-events: all !important; /* 强制启用点击事件 */
}

.shape-element.selected {
  stroke: #ffd700 !important;
  stroke-width: 3px !important;
  filter: drop-shadow(0 0 3px rgba(255, 215, 0, 0.5)) !important;
}

.shape-element.shape-edit-mode {
  stroke: #ff4500 !important;
  stroke-width: 3px !important;
  stroke-dasharray: none !important;
  filter: drop-shadow(0 0 5px rgba(255, 69, 0, 0.5)) !important;
  pointer-events: all !important;
}

/* 控制点样式 */
.control-point {
  stroke: white;
  stroke-width: 2px;
  fill: #1890ff;
  cursor: pointer;
  pointer-events: all !important; /* 确保控制点可点击 */
  z-index: 100 !important;
}

.control-point:hover {
  fill: #ff4500;
}

.corner-point {
  cursor: nwse-resize;
}

.midpoint {
  cursor: move;
}

.endpoint {
  cursor: crosshair;
}

/* 辅助线样式 */
.alignment-guide {
  stroke: #ff4500;
  stroke-width: 1px;
  stroke-dasharray: 5, 3;
  pointer-events: none;
  z-index: 1000;
}

.horizontal-guide {
  stroke: #ff4500;
}

.vertical-guide {
  stroke: #1890ff;
}

/* 导出连线表对话框样式 */
.export-options {
  margin-bottom: 16px;
}

.export-option {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.export-option input[type="radio"] {
  margin-right: 8px;
}

.connection-preview {
  margin-bottom: 16px;
}

.connection-table {
  width: 100%;
  border-collapse: collapse;
}

.connection-table th,
.connection-table td {
  padding: 8px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.connection-count {
  font-size: 14px;
  color: #666;
}

.no-connections {
  font-size: 14px;
  color: #999;
}

/* 连线列表样式 */
.connection-list-panel {
  position: relative;
  background-color: #f8f9fa;
  border-top: 1px solid #e0e0e0;
  height: 280px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: height 0.2s ease;
}

.panel-resizer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: transparent;
  cursor: ns-resize;
  z-index: 10;
}

.panel-resizer:hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background: rgba(24, 144, 255, 0.5);
  border-radius: 1.5px;
}

@media (max-height: 600px) {
  .connection-list-panel {
    height: 180px;
  }
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f1f3f5;
}

.panel-title {
  font-weight: 600;
  font-size: 14px;
  color: #333;
}

.panel-close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #888;
}

.panel-close-btn:hover {
  color: #333;
}

.panel-filters {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #e0e0e0;
  background-color: #fff;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 5px;
}

.filter-input {
  padding: 6px 10px;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  width: 250px;
}

.filter-select {
  padding: 6px 10px;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  background-color: #fff;
}

.sort-direction-btn {
  background: #f5f5f5;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.connection-table-container {
  flex: 1;
  overflow-y: auto;
  padding: 0 10px 10px;
}

.connection-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 5px;
}

.connection-table th {
  background-color: #f1f3f5;
  padding: 8px;
  text-align: left;
  font-weight: 600;
  font-size: 12px;
  border-bottom: 2px solid #e0e0e0;
  position: sticky;
  top: 0;
  cursor: pointer;
  user-select: none;
}

.connection-table th.active {
  background-color: #e7f1ff;
}

.connection-table th:hover {
  background-color: #e6e6e6;
}

.connection-table td {
  padding: 8px;
  font-size: 13px;
  border-bottom: 1px solid #e0e0e0;
}

.connection-table tr:hover {
  background-color: #f0f8ff;
}

.connection-table tr.selected {
  background-color: #e7f1ff;
}

.sort-icon {
  margin-left: 5px;
  font-weight: bold;
}

.action-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 3px;
  margin: 0 2px;
  border-radius: 3px;
}

.locate-btn {
  color: #1890ff;
}

.locate-btn:hover {
  background-color: rgba(24, 144, 255, 0.1);
}

.action-btn.delete-btn {
  color: #ff4d4f;
}

.action-btn.delete-btn:hover {
  background-color: rgba(255, 77, 79, 0.1);
}

.connection-count {
  padding: 8px;
  color: #666;
  font-size: 12px;
  text-align: right;
}

.action-button {
  padding: 5px 10px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.action-button:hover {
  background-color: #40a9ff;
}

/* 高亮动画 */
@keyframes highlight-pulse {
  0% { stroke-width: 2px; stroke-opacity: 1; }
  50% { stroke-width: 6px; stroke-opacity: 0.7; }
  100% { stroke-width: 2px; stroke-opacity: 1; }
}

.highlight-pulse {
  animation: highlight-pulse 1s ease-in-out infinite;
}
/* 图标现代化样式 */
.modern-icon {
  filter: drop-shadow(var(--icon-shadow));
  transition: all 0.2s ease;
}

.toolbar-item:hover .modern-icon {
  filter: drop-shadow(var(--icon-shadow-hover));
  transform: translateY(-1px);
}

.toolbar-item.active .modern-icon {
  filter: drop-shadow(0 2px 6px rgba(24,144,255,0.4));
}

/* 设备图标现代化样式 */
.device-svg {
  filter: drop-shadow(var(--icon-shadow));
  transition: all 0.3s ease;
}

.device-item:hover .device-svg {
  filter: drop-shadow(var(--icon-shadow-hover));
  transform: scale(1.05);
}

.canvas-device-icon svg {
  filter: drop-shadow(var(--icon-shadow));
  transition: all 0.3s ease;
}

.canvas-device.selected .canvas-device-icon svg {
  filter: drop-shadow(0 4px 10px rgba(24,144,255,0.5));
  transform: scale(1.08);
}

/* 改进的工具栏样式 */
.toolbar-item {
  transition: background-color 0.2s ease, transform 0.1s ease, box-shadow 0.2s ease;
  border-radius: 4px;
}

.toolbar-item:hover {
  background-color: var(--hover-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0,0,0,0.08);
}

.toolbar-item.active {
  background-color: var(--active-color);
  box-shadow: 0 2px 6px rgba(24,144,255,0.2);
  border-bottom: 2px solid var(--primary-color);
}
</style>