<template>
  <div class="rack-management-container">
    <!-- 测试按钮，使用isDev变量替代直接检查 -->
    <!-- <el-button v-if="isDev" @click="testBackendConnection" type="warning">
      测试后端连接
    </el-button> -->
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="statistics-cards">
      <el-col :span="6">
        <el-card shadow="hover" class="statistic-card">
          <div class="statistic-card-content">
            <div class="statistic-info">
              <div class="statistic-title">机柜总数</div>
              <div class="statistic-value">{{ rackStats.totalRacks }}</div>
              <div class="statistic-extra">使用率 {{ rackStats.usageRate }}%</div>
            </div>
            <div class="statistic-icon">
              <el-icon><Grid /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="statistic-card">
          <div class="statistic-card-content">
            <div class="statistic-info">
              <div class="statistic-title">设备总数</div>
              <div class="statistic-value">{{ rackStats.totalDevices }}</div>
              <div class="statistic-extra">在线 {{ rackStats.onlineDevices }} 台</div>
            </div>
            <div class="statistic-icon">
              <el-icon><Monitor /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="statistic-card">
          <div class="statistic-card-content">
            <div class="statistic-info">
              <div class="statistic-title">告警数量</div>
              <div class="statistic-value">{{ rackStats.alerts }}</div>
              <div class="statistic-extra">较昨日 {{ rackStats.alertTrend }}</div>
            </div>
            <div class="statistic-icon warning">
              <el-icon><Warning /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="statistic-card">
          <div class="statistic-card-content">
            <div class="statistic-info">
              <div class="statistic-title">平均温度</div>
              <div class="statistic-value">{{ rackStats.avgTemperature }}°C</div>
              <div class="statistic-extra">{{ rackStats.tempStatus }}</div>
            </div>
            <div class="statistic-icon">
              <el-icon><Reading /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 机柜列表 -->
    <el-card class="rack-list-card">
      <template #header>
        <div class="rack-list-header">
          <div class="rack-list-title">
            <h2>机柜列表</h2>
            <span class="subtitle">管理所有机柜及其设备</span>
          </div>
          <div class="rack-list-actions">
            <el-input
              v-model="searchQuery"
              placeholder="搜索机柜..."
              prefix-icon="el-icon-search"
              class="search-input"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-button type="info" @click="navigateToDatacenterManagement">
              <el-icon><House /></el-icon>机房管理
            </el-button>
            <el-button type="primary" @click="showAddRackDialog">
              <el-icon><Plus /></el-icon>添加机柜
            </el-button>
            <el-button type="success" @click="showAllRacks">
              <el-icon><View /></el-icon>全部机柜
            </el-button>
            <el-button type="primary" @click="showImportExportDialog">
              <el-icon><Document /></el-icon>导入数据
            </el-button>
            <el-button type="success" @click="exportRackData" :loading="exportLoading">
              <el-icon><Download /></el-icon>导出机柜
            </el-button>
            <el-button @click="toggleFilterPanel">
              <el-icon><Setting /></el-icon>
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table
        :data="filteredRacks"
        style="width: 100%"
        :default-sort="{ prop: 'id', order: 'ascending' }"
        @row-click="handleRackClick"
        v-loading="loading"
      >
        <template #empty>
          <div class="empty-data-container">
            <el-empty 
              description="暂无机柜数据" 
              :image-size="120"
            >
              <template #description>
                <p>未找到机柜数据，您可以：</p>
                <ol class="empty-action-list">
                  <li>点击"添加机柜"按钮创建新机柜</li>
                  <li>检查后端服务是否正常运行</li>
                  <li>尝试刷新页面重新加载数据</li>
                </ol>
              </template>
              <el-button type="primary" @click="showAddRackDialog">
                <el-icon><Plus /></el-icon>添加机柜
              </el-button>
            </el-empty>
          </div>
        </template>
        
        <el-table-column prop="location" label="机房" min-width="120" sortable />
        <el-table-column prop="id" label="机柜编号" min-width="120" sortable />
        <el-table-column prop="name" label="名称" min-width="160" sortable />
        <el-table-column prop="capacity" label="容量" min-width="120">
          <template #default="scope">
            <span>{{ scope.row.used_u }}U/{{ scope.row.total_u }}U</span>
          </template>
        </el-table-column>
        <el-table-column prop="device_count" label="设备数" min-width="100" sortable />
        <el-table-column prop="temperature" label="温度" min-width="100" sortable>
          <template #default="scope">
            <span>{{ scope.row.temperature }}°C</span>
          </template>
        </el-table-column>
        <el-table-column prop="power" label="功率" min-width="100" sortable>
          <template #default="scope">
            <span>{{ scope.row.power }}kW</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" min-width="120">
          <template #default="scope">
            <el-tag
              :type="scope.row.status === 'normal' ? 'success' : 'danger'"
              size="small"
              effect="light"
              round
            >
              <el-icon v-if="scope.row.status === 'normal'"><CircleCheck /></el-icon>
              <el-icon v-else><Warning /></el-icon>
              {{ scope.row.status === 'normal' ? '正常' : '告警' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" min-width="180">
          <template #default="scope">
            <div class="operation-buttons">
              <el-button
                type="primary"
                size="small"
                @click.stop="editRack(scope.row)"
              >
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click.stop="confirmDeleteRack(scope.row)"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
              <el-button
                type="success"
                size="small"
                @click.stop="navigateToRackDetail(scope.row.id)"
              >
                <el-icon><ArrowRight /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          v-model:currentPage="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalRacks"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 添加机柜对话框 -->
    <add-rack-dialog
      v-model:visible="addRackDialogVisible"
      @add-rack="handleAddRack"
    />

    <!-- 编辑机柜对话框 -->
    <edit-rack-dialog
      v-model:visible="editRackDialogVisible"
      :rack="currentRack"
      @update-rack="handleUpdateRack"
    />

    <!-- 导入/导出机柜数据对话框 -->
    <import-export-rack-dialog
      v-model:visible="importExportDialogVisible"
      :available-racks="racks"
      @import-completed="handleImportCompleted"
    />
    
    <!-- 添加机房对话框 -->
    <add-datacenter-dialog
      v-model:visible="addDatacenterDialogVisible"
      @add-datacenter="handleAddDatacenter"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { Grid, Monitor, Warning, Reading, Search, Plus, Setting, CircleCheck, ArrowRight, View, Document, Edit, Delete, House, Download } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import axios from 'axios';
import AddRackDialog from '@/components/rackManagement/dialogs/AddRackDialog.vue';
import EditRackDialog from '@/components/rackManagement/dialogs/EditRackDialog.vue';
import ImportExportRackDialog from '@/components/rackManagement/dialogs/ImportExportRackDialog.vue';
import AddDatacenterDialog from '@/components/rackManagement/dialogs/AddDatacenterDialog.vue';
import * as XLSX from 'xlsx';

// 判断是否为开发环境
const isDev = import.meta.env.DEV;

// 设置axios默认配置
axios.defaults.headers.post['Content-Type'] = 'application/json';
axios.defaults.headers.put['Content-Type'] = 'application/json';

// 定义API地址
const API_URL = '/api/rack-management/';

const router = useRouter();
const searchQuery = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const totalRacks = ref(0);
const addRackDialogVisible = ref(false);
const editRackDialogVisible = ref(false);
const importExportDialogVisible = ref(false);
const addDatacenterDialogVisible = ref(false);
const loading = ref(false);
const exportLoading = ref(false);
const currentRack = ref(null);

// 机柜统计数据
const rackStats = reactive({
  totalRacks: 0,
  usageRate: 0,
  totalDevices: 0,
  onlineDevices: 0,
  alerts: 0,
  alertTrend: '0',
  avgTemperature: '0',
  tempStatus: '正常范围内'
});

// 初始化为数组，防止在任何情况下变成非数组
const racks = ref([]);

// 过滤和分页
const filteredRacks = computed(() => {
  // 确保racks.value存在且是数组
  if (!Array.isArray(racks.value)) {
    console.error("racks.value 不是数组", racks.value);
    return [];
  }
  
  let result = [...racks.value];
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(rack => 
      (rack.id && rack.id.toLowerCase().includes(query)) ||
      (rack.name && rack.name.toLowerCase().includes(query)) ||
      (rack.location && rack.location.toLowerCase().includes(query))
    );
  }
  
  totalRacks.value = result.length;
  
  // 分页
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  
  return result.slice(start, end);
});

const handleSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1;
};

const handleCurrentChange = (page) => {
  currentPage.value = page;
};

// 获取统计数据
const fetchRackStats = async () => {
  try {
    console.log("开始获取机柜统计数据...");
    
    // 1. 先尝试调试端点
    try {
      const statsDebugEndpoint = API_URL.replace(/\/$/, '') + "/debug-stats";
      const debugResponse = await axios.get(statsDebugEndpoint);
      console.log("调试统计接口返回:", debugResponse.data);
    } catch (debugError) {
      console.error("调试统计接口错误:", debugError);
    }
    
    // 2. 再获取实际统计数据
    const statsEndpoint = API_URL.replace(/\/$/, '') + "/stats";
    const response = await axios.get(statsEndpoint);
    console.log("获取到统计数据:", response.data);
    
    if (response && response.data) {
      // 将后端数据合并到本地状态
      Object.assign(rackStats, response.data);
      
      // 确保必需的字段都存在，防止前端渲染错误
      if (typeof rackStats.usageRate !== 'number') rackStats.usageRate = 0;
      if (typeof rackStats.totalRacks !== 'number') rackStats.totalRacks = 0;
      if (typeof rackStats.totalDevices !== 'number') rackStats.totalDevices = 0;
      if (typeof rackStats.onlineDevices !== 'number') rackStats.onlineDevices = 0;
      if (typeof rackStats.alerts !== 'number') rackStats.alerts = 0;
      
      console.log("统计数据已更新:", rackStats);
    } else {
      console.warn("获取到的统计数据为空");
    }
  } catch (error) {
    console.error("获取机柜统计数据失败:", error);
    // 如果是网络错误，显示更具体的信息
    const errorMessage = error.response?.data?.detail || error.message || "未知错误";
    ElMessage.error(`获取统计数据失败: ${errorMessage}`);
    
    // 设置默认值，确保UI不会崩溃
    Object.assign(rackStats, {
      totalRacks: 0,
      usageRate: 0,
      totalDevices: 0,
      onlineDevices: 0,
      alerts: 0,
      alertTrend: '0',
      avgTemperature: '0.0',
      tempStatus: '获取数据失败'
    });
  }
};

// 获取机柜列表
const fetchRacks = async () => {
  loading.value = true;
  try {
    console.log("开始获取机柜列表...");
    
    // 使用封装好的去除尾斜杠的API端点
    const apiEndpoint = API_URL.endsWith('/') ? API_URL : API_URL + '/';
    console.log("请求URL:", apiEndpoint);
    
    // 添加额外的调试信息
    try {
      console.log("检查API服务健康状态...");
      const healthCheck = await axios.get(API_URL.replace(/\/$/, '') + "/health-check");
      console.log("API健康检查结果:", healthCheck.data);
    } catch (healthError) {
      console.error("API健康检查失败:", healthError);
    }
    
    console.log("发送请求获取机柜列表...");
    const response = await axios.get(apiEndpoint);
    console.log("API返回状态码:", response.status);
    console.log("API返回数据类型:", typeof response.data);
    console.log("API返回数据:", response.data);
    
    // 确保racks.value始终是数组
    if (response && response.data) {
      if (Array.isArray(response.data)) {
        racks.value = response.data;
        console.log(`解析后的racks数据: ${racks.value.length}个机柜`);
        
        // 调试前5个机柜
        if (racks.value.length > 0) {
          console.log("前5个机柜数据样例:");
          racks.value.slice(0, 5).forEach((rack, index) => {
            console.log(`[${index}] ID: ${rack.id}, 名称: ${rack.name}, 位置: ${rack.location}`);
          });
        }
      } else {
        console.error("API返回的数据不是数组:", response.data);
        racks.value = [];
      }
    } else {
      racks.value = [];
      console.warn("API响应格式异常，设置为空数组");
    }
    
    totalRacks.value = racks.value.length;
    loading.value = false;
  } catch (error) {
    console.error("获取机柜列表失败:", error);
    console.error("错误详情:", error.response?.data || error.message);
    console.error("请求配置:", error.config);
    ElMessage.error(`获取机柜列表失败: ${error.response?.data?.detail || error.message}`);
    // 确保错误时也设置为空数组
    racks.value = [];
    loading.value = false;
  }
};

const showAddRackDialog = () => {
  addRackDialogVisible.value = true;
};

const showAddDatacenterDialog = () => {
  addDatacenterDialogVisible.value = true;
};

const handleAddRack = async (newRack) => {
  try {
    // 发送表单数据到后端API
    console.log("发送到后端的数据：", newRack);
    
    // 确保表单字段名与后端模型匹配
    const rackData = {
      id: newRack.id, // 添加机柜编号字段
      name: newRack.name,
      location: newRack.location || "",
      datacenter_id: newRack.datacenter_id || null, // 确保包含datacenter_id
      total_u: parseInt(newRack.total_u),  // 确保是整数
      width: parseInt(newRack.width),      // 确保是整数 
      depth: parseInt(newRack.depth),      // 确保是整数
      temperature: parseFloat(newRack.temperature),  // 确保是浮点数
      humidity: parseFloat(newRack.humidity),        // 确保是浮点数
      max_power: parseFloat(newRack.max_power),      // 确保是浮点数
      description: newRack.description || "",
      status: newRack.status || "normal"
    };
    
    console.log("处理后准备发送的数据:", rackData);
    
    // 先测试调试端点
    try {
      const debugEndpoint = API_URL.endsWith('/') ? API_URL + "debug" : API_URL + "/debug";
      console.log("调试端点URL:", debugEndpoint);
      const debugResponse = await axios.post(debugEndpoint, rackData);
      console.log("调试端点响应:", debugResponse.data);
    } catch (debugError) {
      console.error("调试端点错误:", debugError);
    }
    
    // 使用正确格式的URL发送请求，直接使用API_URL，确保URL格式正确
    console.log("POST请求URL:", API_URL);
    
    const response = await axios.post(API_URL, rackData);
    if (response.data) {
      console.log("添加机柜成功，服务器返回:", response.data);
      racks.value.push(response.data);
      totalRacks.value = racks.value.length;
      
      // 更新统计数据
      fetchRackStats();
      
      ElMessage.success(`机柜 ${newRack.name} 添加成功`);
    }
  } catch (error) {
    console.error("添加机柜失败:", error);
    ElMessage.error(`添加机柜失败: ${error.response?.data?.detail || error.message}`);
  }
};

const navigateToRackDetail = (rackId) => {
  console.log(`正在导航到机柜详情页面，机柜ID: ${rackId}`);
  
  if (!rackId) {
    console.error("机柜ID为空或无效:", rackId);
    ElMessage.warning("无法查看详情：机柜ID无效");
    return;
  }
  
  // 打印当前点击的机柜数据以辅助调试
  const clickedRack = racks.value.find(rack => rack.id === rackId);
  if (clickedRack) {
    console.log("导航到的机柜数据:", clickedRack);
  } else {
    console.warn(`在当前数据中找不到ID为 ${rackId} 的机柜`);
  }
  
  // 直接导航到详情页，不再进行预检查
  // 详情页会处理设备列表的获取和错误处理
  router.push(`/rack-detail/${rackId}`);
};

const handleRackClick = (row) => {
  navigateToRackDetail(row.id);
};

const toggleFilterPanel = () => {
  // 实现筛选面板
  ElMessage.info('筛选功能待实现');
};

// 显示全部机柜视图
const showAllRacks = () => {
  // 导航到全部机柜视图页面
  router.push('/rack-visualization');
};

// 显示导入/导出对话框
const showImportExportDialog = () => {
  importExportDialogVisible.value = true;
};

// 编辑机柜
const editRack = (rack) => {
  // 设置当前选中的机柜
  currentRack.value = { ...rack };
  // 显示编辑对话框
  editRackDialogVisible.value = true;
};

// 确认删除机柜
const confirmDeleteRack = (rack) => {
  ElMessageBox.confirm(
    `确定要删除机柜 ${rack.name} 吗？此操作将同时删除该机柜下的所有设备，且不可恢复。`,
    '删除确认',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
      confirmButtonClass: 'el-button--danger'
    }
  ).then(() => {
    deleteRack(rack.id);
  }).catch(() => {
    // 用户取消操作
    ElMessage.info('已取消删除操作');
  });
};

// 删除机柜
const deleteRack = async (rackId) => {
  try {
    console.log(`正在删除机柜，ID: ${rackId}`);
    const deleteUrl = `${API_URL}${rackId}`;
    console.log(`删除请求URL: ${deleteUrl}`);
    
    const response = await axios.delete(deleteUrl);
    console.log('删除响应:', response.data);
    
    // 从本地数据中移除
    racks.value = racks.value.filter(rack => rack.id !== rackId);
    
    // 更新统计数据
    await fetchRackStats();
    
    ElMessage.success('机柜已成功删除');
  } catch (error) {
    console.error('删除机柜失败:', error);
    ElMessage.error(`删除失败: ${error.response?.data?.detail || error.message}`);
  }
};

// 更新机柜信息
const handleUpdateRack = async (updatedRack) => {
  loading.value = true;
  try {
    console.log('更新机柜信息:', updatedRack);
    const response = await axios.put(`${API_URL}${currentRack.value.id}`, {
      id: updatedRack.id,
      name: updatedRack.name,
      datacenter_id: updatedRack.datacenter_id,
      location: updatedRack.location,
      total_u: updatedRack.total_u,
      status: updatedRack.status,
      description: updatedRack.description
    });
    
    if (response.status === 200) {
      ElMessage.success('机柜信息更新成功');
      
      // 如果机柜ID发生变化，需要更新表格中的数据
      const index = racks.value.findIndex(r => r.id === currentRack.value.id);
      if (index !== -1) {
        // 如果ID变了，需要删除旧记录，添加新记录
        if (updatedRack.id !== currentRack.value.id) {
          racks.value.splice(index, 1);
          racks.value.push(response.data);
        } else {
          // 否则直接更新当前记录
        racks.value[index] = response.data;
        }
      }
      
      // 关闭编辑对话框
      editRackDialogVisible.value = false;
      
      // 刷新机柜列表和统计数据
      await fetchRacks();
      await fetchRackStats();
    }
  } catch (error) {
    console.error('更新机柜信息失败:', error);
    ElMessage.error(`更新机柜信息失败: ${error.response?.data?.detail || error.message}`);
  } finally {
    loading.value = false;
  }
};

// 处理导入完成事件
const handleImportCompleted = async () => {
  ElMessage.success('机柜数据导入成功，正在刷新列表...');
  // 刷新数据
  await fetchRacks();
  await fetchRackStats();
};

// 测试后端连接
const testBackendConnection = async () => {
  try {
    // 测试健康检查端点
    const healthCheckEndpoint = API_URL.replace(/\/$/, '') + "/health-check";
    const healthResponse = await axios.get(healthCheckEndpoint);
    console.log("健康检查响应:", healthResponse.data);
    
    // 测试GET统计端点
    try {
      const statsEndpoint = API_URL.replace(/\/$/, '') + "/stats";
      const statsResponse = await axios.get(statsEndpoint);
      console.log("统计接口响应:", statsResponse.data);
    } catch (statsError) {
      console.error("统计接口失败:", statsError);
    }
    
    // 添加调试统计端点测试
    try {
      const debugStatsEndpoint = API_URL.replace(/\/$/, '') + "/debug-stats";
      const debugStatsResponse = await axios.get(debugStatsEndpoint);
      console.log("调试统计接口响应:", debugStatsResponse.data);
    } catch (debugStatsError) {
      console.error("调试统计接口失败:", debugStatsError);
    }
    
    // 测试POST调试端点
    const debugEndpoint = API_URL.replace(/\/$/, '') + "/debug";
    console.log("调试端点URL:", debugEndpoint);
    const debugResponse = await axios.post(debugEndpoint, {test: 'data'});
    console.log("调试端点响应:", debugResponse.data);
    
    // ElMessage.success('后端连接测试成功!');
  } catch (error) {
    console.error("后端连接测试失败:", error);
    ElMessage.error(`后端连接测试失败: ${error.response?.data?.detail || error.message}`);
  }
};

// 处理添加机房
const handleAddDatacenter = async (newDatacenter) => {
  try {
    // 发送表单数据到后端API
    console.log("发送到后端的数据：", newDatacenter);
    
    // 确保表单字段名与后端模型匹配
    const datacenterData = {
      name: newDatacenter.name,
      location: newDatacenter.location || "",
      description: newDatacenter.description || ""
    };
    
    console.log("处理后准备发送的数据:", datacenterData);
    
    // 使用正确格式的URL发送请求
    const datacenterApiUrl = '/api/datacenter/';
    
    const response = await axios.post(datacenterApiUrl, datacenterData);
    if (response.data) {
      console.log("添加机房成功，服务器返回:", response.data);
      
      // 刷新机柜列表，因为机房可能会影响机柜显示
      await fetchRacks();
      
      ElMessage.success(`机房 ${newDatacenter.name} 添加成功`);
    }
  } catch (error) {
    console.error("添加机房失败:", error);
    ElMessage.error(`添加机房失败: ${error.response?.data?.detail || error.message}`);
  }
};

// 导航到机房管理页面
const navigateToDatacenterManagement = () => {
  router.push('/datacenter-management');
};

// 获取机柜设备数据
const fetchRackDevices = async (rackId) => {
  try {
    const response = await axios.get(`${API_URL}${rackId}/devices`);
    return response.data || [];
  } catch (error) {
    console.error(`获取机柜 ${rackId} 设备数据失败:`, error);
    return [];
  }
};

// 获取机房数据
const fetchDatacenters = async () => {
  try {
    const response = await axios.get('/api/datacenter/');
    return response.data || [];
  } catch (error) {
    console.error('获取机房数据失败:', error);
    return [];
  }
};

// 生成机柜立面图数据
const generateRackElevation = (rack, devices) => {
  const elevation = [];
  const totalU = rack.total_u || 42;

  // 创建U位占用映射
  const uPositions = new Array(totalU + 1).fill(null); // 1-based indexing

  // 标记设备占用的U位
  devices.forEach(device => {
    const startPos = device.position;
    const endPos = startPos + (device.u_size || 1) - 1;

    for (let u = startPos; u <= endPos && u <= totalU; u++) {
      uPositions[u] = {
        deviceName: device.name,
        deviceType: device.type || '未知',
        deviceModel: device.model || '',
        isStart: u === startPos,
        isEnd: u === endPos,
        uSize: device.u_size || 1
      };
    }
  });

  // 生成立面图数据（从上到下，U42到U1）
  for (let u = totalU; u >= 1; u--) {
    const device = uPositions[u];
    elevation.push([
      `U${u}`,
      device ? device.deviceName : '空闲',
      device ? device.deviceType : '',
      device ? device.deviceModel : '',
      device ? (device.isStart ? `${device.uSize}U设备` : '↑') : ''
    ]);
  }

  return elevation;
};

// 生成机柜视图数据（完全按照用户图片样式的表格形式机柜图）
const generateRackView = (racks, allDevicesMap) => {
  const rackView = [];
  const racksPerRow = 3; // 每行显示3个机柜，类似图片效果

  // 将机柜分组，每组包含racksPerRow个机柜
  const rackRows = [];
  for (let i = 0; i < racks.length; i += racksPerRow) {
    rackRows.push(racks.slice(i, i + racksPerRow));
  }

  // 为每组机柜生成视图
  rackRows.forEach((rackGroup, groupIndex) => {
    // 为每个机柜创建U位占用映射
    const rackMaps = rackGroup.map(rack => {
      const devices = allDevicesMap[rack.id] || [];
      const uPositions = new Array(47).fill(null); // 1-based indexing, 支持到U46

      devices.forEach(device => {
        const startPos = device.position;
        const endPos = startPos + (device.u_size || 1) - 1;

        for (let u = startPos; u <= endPos && u <= 46; u++) {
          uPositions[u] = {
            deviceName: device.name,
            deviceType: device.type || '未知',
            isStart: u === startPos,
            isEnd: u === endPos,
            uSize: device.u_size || 1,
            status: device.status || 'unknown'
          };
        }
      });

      return { rack, uPositions };
    });

    // 添加机柜标题行
    const titleRow = [];
    rackMaps.forEach((rackMap, index) => {
      const rack = rackMap.rack;
      // 左U位列
      titleRow.push('');
      // 机柜标题列
      titleRow.push(`${rack.name}-${rack.max_power || '5000'}W`);
      // 右U位列
      titleRow.push('');

      if (index < rackMaps.length - 1) {
        titleRow.push(''); // 机柜间隔列
      }
    });
    rackView.push(titleRow);

    // 生成U位行（从U46到U1）
    for (let u = 46; u >= 1; u--) {
      const row = [];

      rackMaps.forEach((rackMap, rackIndex) => {
        const { uPositions } = rackMap;
        const uLabel = u.toString();

        // 左侧U位标号
        row.push(uLabel);

        // 中间设备内容
        const device = uPositions[u];
        let deviceContent = '';

        if (device) {
          if (device.isStart) {
            // 设备起始位置，显示设备名称
            deviceContent = device.deviceName;
          } else {
            // 设备延续位置，显示设备名称（保持一致）
            deviceContent = device.deviceName;
          }
        } else {
          // 空闲位置
          deviceContent = '';
        }

        row.push(deviceContent);

        // 右侧U位标号
        row.push(uLabel);

        // 在机柜之间添加分隔列
        if (rackIndex < rackMaps.length - 1) {
          row.push(''); // 机柜间隔列
        }
      });

      rackView.push(row);
    }

    // 在机柜组之间添加分隔行
    if (groupIndex < rackRows.length - 1) {
      rackView.push(new Array(titleRow.length).fill(''));
      rackView.push(new Array(titleRow.length).fill(''));
    }
  });

  return rackView;
};

// 增强的导出机柜数据函数
const exportRackData = async () => {
  try {
    exportLoading.value = true;

    // 获取当前显示的机柜数据
    const racksToExport = filteredRacks.value;

    if (!racksToExport || racksToExport.length === 0) {
      ElMessage.warning('没有可导出的机柜数据');
      return;
    }

    // 生成文件名（包含当前日期）
    const now = new Date();
    const dateStr = now.getFullYear() + '-' +
                   String(now.getMonth() + 1).padStart(2, '0') + '-' +
                   String(now.getDate()).padStart(2, '0');
    const fileName = `机柜管理数据_${dateStr}`;

    // 创建工作簿
    const wb = XLSX.utils.book_new();

    // 步骤1: 导出机柜基础数据
    ElMessage.info('正在准备机柜基础数据...');
    const rackData = [
      ['机柜编号', '机柜名称', '机房位置', '机房ID', '总容量(U)', '已使用(U)', '设备数量', '温度(°C)', '湿度(%)', '当前功率(kW)', '最大功率(kW)', '状态', '描述', '创建时间']
    ];

    racksToExport.forEach(rack => {
      rackData.push([
        rack.id || '',
        rack.name || '',
        rack.location || '',
        rack.datacenter_id || '',
        rack.total_u || 0,
        rack.used_u || 0,
        rack.device_count || 0,
        rack.temperature || 0,
        rack.humidity || 0,
        rack.power || 0,
        rack.max_power || 0,
        rack.status === 'normal' ? '正常' : '告警',
        rack.description || '',
        rack.created_at ? new Date(rack.created_at).toLocaleString() : ''
      ]);
    });

    const wsRacks = XLSX.utils.aoa_to_sheet(rackData);
    wsRacks['!cols'] = [
      { wch: 12 }, { wch: 15 }, { wch: 15 }, { wch: 12 }, { wch: 10 },
      { wch: 10 }, { wch: 8 }, { wch: 8 }, { wch: 8 }, { wch: 10 },
      { wch: 10 }, { wch: 8 }, { wch: 20 }, { wch: 18 }
    ];
    XLSX.utils.book_append_sheet(wb, wsRacks, '机柜数据');

    // 步骤2: 获取并导出设备数据
    ElMessage.info('正在获取设备详细数据...');
    const allDevices = [];
    const rackElevations = [];
    const allDevicesMap = {}; // 存储所有机柜的设备数据，用于机柜视图

    for (let i = 0; i < racksToExport.length; i++) {
      const rack = racksToExport[i];
      ElMessage.info(`正在处理机柜 ${rack.id} (${i + 1}/${racksToExport.length})...`);

      try {
        const devices = await fetchRackDevices(rack.id);
        allDevicesMap[rack.id] = devices; // 保存设备数据用于机柜视图

        // 收集设备数据
        devices.forEach(device => {
          allDevices.push([
            rack.id,
            rack.name,
            device.name || '',
            device.type || '',
            device.model || '',
            device.position || 0,
            device.u_size || 1,
            device.power || 0,
            device.weight || 0,
            device.status === 'online' ? '在线' : '离线',
            device.temperature || 0,
            device.serial_number || '',
            device.ip_address || '',
            device.description || ''
          ]);
        });

        // 生成机柜立面图
        const elevation = generateRackElevation(rack, devices);
        rackElevations.push({
          rackId: rack.id,
          rackName: rack.name,
          elevation: elevation
        });

      } catch (error) {
        console.error(`处理机柜 ${rack.id} 时出错:`, error);
        allDevicesMap[rack.id] = []; // 设置为空数组避免后续错误
        // 继续处理其他机柜
      }
    }

    // 步骤3: 创建设备数据工作表
    if (allDevices.length > 0) {
      ElMessage.info('正在生成设备数据工作表...');
      const deviceData = [
        ['机柜编号', '机柜名称', '设备名称', '设备类型', '设备型号', 'U位置', 'U高度', '功率(W)', '重量(kg)', '状态', '温度(°C)', '序列号', 'IP地址', '描述']
      ];
      deviceData.push(...allDevices);

      const wsDevices = XLSX.utils.aoa_to_sheet(deviceData);
      wsDevices['!cols'] = [
        { wch: 12 }, { wch: 15 }, { wch: 15 }, { wch: 12 }, { wch: 15 },
        { wch: 8 }, { wch: 8 }, { wch: 10 }, { wch: 10 }, { wch: 8 },
        { wch: 10 }, { wch: 15 }, { wch: 15 }, { wch: 20 }
      ];
      XLSX.utils.book_append_sheet(wb, wsDevices, '设备数据');
    }

    // 步骤4: 创建机柜立面图工作表
    if (rackElevations.length > 0) {
      ElMessage.info('正在生成机柜立面图...');
      const elevationData = [['机柜编号', '机柜名称', 'U位', '设备名称', '设备类型', '设备型号', '备注']];

      rackElevations.forEach(rackElev => {
        rackElev.elevation.forEach(row => {
          elevationData.push([
            rackElev.rackId,
            rackElev.rackName,
            ...row
          ]);
        });
        // 添加分隔行
        elevationData.push(['', '', '', '', '', '', '']);
      });

      const wsElevation = XLSX.utils.aoa_to_sheet(elevationData);
      wsElevation['!cols'] = [
        { wch: 12 }, { wch: 15 }, { wch: 8 }, { wch: 15 }, { wch: 12 }, { wch: 15 }, { wch: 15 }
      ];
      XLSX.utils.book_append_sheet(wb, wsElevation, '机柜立面图');
    }

    // 步骤5: 创建机柜视图工作表（并排显示机柜）
    if (racksToExport.length > 0) {
      ElMessage.info('正在生成机柜视图...');
      const rackViewData = generateRackView(racksToExport, allDevicesMap);

      const wsRackView = XLSX.utils.aoa_to_sheet(rackViewData);

      // 设置列宽以适应表格形式机柜视图
      const colWidths = [];
      const maxCols = Math.max(...rackViewData.map(row => row.length));
      for (let i = 0; i < maxCols; i++) {
        const colIndex = i % 4; // 每个机柜占用4列：左U位、设备内容、右U位、分隔
        if (colIndex === 0 || colIndex === 2) {
          colWidths.push({ wch: 3 }); // U位列，较窄
        } else if (colIndex === 1) {
          colWidths.push({ wch: 25 }); // 设备内容列，较宽
        } else {
          colWidths.push({ wch: 2 }); // 分隔列，最窄
        }
      }
      wsRackView['!cols'] = colWidths;

      // 设置行高
      const rowHeights = [];
      for (let i = 0; i < rackViewData.length; i++) {
        rowHeights[i] = { hpt: 16 }; // 设置行高为16磅，稍微高一点
      }
      wsRackView['!rows'] = rowHeights;

      // 为每个机柜添加独立的边框
      const range = XLSX.utils.decode_range(wsRackView['!ref']);
      const colsPerRack = 4; // 每个机柜占用4列：左U位、设备内容、右U位、分隔

      for (let R = range.s.r; R <= range.e.r; ++R) {
        for (let C = range.s.c; C <= range.e.c; ++C) {
          const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
          if (!wsRackView[cellAddress]) {
            wsRackView[cellAddress] = { t: 's', v: '' };
          }

          // 添加边框样式
          if (!wsRackView[cellAddress].s) {
            wsRackView[cellAddress].s = {};
          }

          const colIndex = C % colsPerRack;

          // 只为机柜相关列添加边框（不包括分隔列）
          if (colIndex !== 3) {
            // 确定边框样式
            const borderStyle = { style: 'thin', color: { rgb: '000000' } };
            const thickBorderStyle = { style: 'medium', color: { rgb: '000000' } };

            wsRackView[cellAddress].s.border = {
              top: borderStyle,
              bottom: borderStyle,
              left: colIndex === 0 ? thickBorderStyle : borderStyle,  // 机柜左边界用粗线
              right: colIndex === 2 ? thickBorderStyle : borderStyle   // 机柜右边界用粗线
            };

            // 为机柜标题行和第一个U位行添加粗上边框
            if (R === range.s.r || (R > range.s.r && rackViewData[R-1] && rackViewData[R-1].every(cell => cell === ''))) {
              wsRackView[cellAddress].s.border.top = thickBorderStyle;
            }

            // 为最后一个U位行添加粗下边框
            if (R === range.e.r || (R < range.e.r && rackViewData[R+1] && rackViewData[R+1].every(cell => cell === ''))) {
              wsRackView[cellAddress].s.border.bottom = thickBorderStyle;
            }

            // 设置文本居中对齐
            wsRackView[cellAddress].s.alignment = {
              horizontal: 'center',
              vertical: 'center'
            };
          }
        }
      }

      XLSX.utils.book_append_sheet(wb, wsRackView, '机柜视图');
    }

    // 步骤6: 获取并创建机房汇总工作表
    ElMessage.info('正在获取机房数据...');
    try {
      const datacenters = await fetchDatacenters();
      if (datacenters.length > 0) {
        const datacenterData = [
          ['机房ID', '机房名称', '机房位置', '机房描述', '机柜数量', '设备数量', '创建时间']
        ];

        datacenters.forEach(dc => {
          datacenterData.push([
            dc.id || '',
            dc.name || '',
            dc.location || '',
            dc.description || '',
            dc.rackCount || 0,
            dc.deviceCount || 0,
            dc.created_at ? new Date(dc.created_at).toLocaleString() : ''
          ]);
        });

        const wsDatacenters = XLSX.utils.aoa_to_sheet(datacenterData);
        wsDatacenters['!cols'] = [
          { wch: 12 }, { wch: 15 }, { wch: 20 }, { wch: 25 }, { wch: 10 }, { wch: 10 }, { wch: 18 }
        ];
        XLSX.utils.book_append_sheet(wb, wsDatacenters, '机房汇总');
      }
    } catch (error) {
      console.error('获取机房数据失败:', error);
    }

    // 步骤7: 创建统计汇总工作表
    ElMessage.info('正在生成统计汇总...');
    const statsData = [
      ['统计项目', '数值', '单位'],
      ['机柜总数', racksToExport.length, '个'],
      ['设备总数', allDevices.length, '台'],
      ['在线设备', allDevices.filter(d => d[9] === '在线').length, '台'],
      ['离线设备', allDevices.filter(d => d[9] === '离线').length, '台'],
      ['总U位容量', racksToExport.reduce((sum, rack) => sum + (rack.total_u || 0), 0), 'U'],
      ['已使用U位', racksToExport.reduce((sum, rack) => sum + (rack.used_u || 0), 0), 'U'],
      ['平均温度', (racksToExport.reduce((sum, rack) => sum + (rack.temperature || 0), 0) / racksToExport.length).toFixed(1), '°C'],
      ['总功率', racksToExport.reduce((sum, rack) => sum + (rack.power || 0), 0).toFixed(2), 'kW'],
      ['导出时间', new Date().toLocaleString(), '']
    ];

    const wsStats = XLSX.utils.aoa_to_sheet(statsData);
    wsStats['!cols'] = [{ wch: 15 }, { wch: 12 }, { wch: 8 }];
    XLSX.utils.book_append_sheet(wb, wsStats, '统计汇总');

    // 步骤8: 生成并下载Excel文件
    ElMessage.info('正在生成Excel文件...');
    const excelBinary = XLSX.write(wb, { bookType: 'xlsx', type: 'binary' });

    const buffer = new ArrayBuffer(excelBinary.length);
    const view = new Uint8Array(buffer);
    for (let i = 0; i < excelBinary.length; i++) {
      view[i] = excelBinary.charCodeAt(i) & 0xFF;
    }

    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `${fileName}.xlsx`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    // 成功消息
    const worksheetCount = wb.SheetNames.length;
    ElMessage.success(`成功导出 ${racksToExport.length} 个机柜、${allDevices.length} 台设备的完整数据，包含 ${worksheetCount} 个工作表（含机柜视图）`);

  } catch (error) {
    console.error('导出机柜数据失败:', error);
    ElMessage.error(`导出失败: ${error.message || '未知错误'}`);
  } finally {
    exportLoading.value = false;
  }
};

onMounted(async () => {
  // 先测试后端连接
  await testBackendConnection();

  // 然后获取数据
  await fetchRacks();
  await fetchRackStats();
});
</script>

<style scoped>
.rack-management-container {
  padding: 0; /* Reduce padding to maximize width */
  width: 100%; /* Ensure full width */
  max-width: 100%; /* Prevent any max-width constraints */
  overflow-x: hidden; /* Prevent horizontal scrolling */
}

.statistics-cards {
  margin-top: 10px;
  margin-bottom: 20px;
}

.statistic-card {
  height: 120px;
  overflow: hidden;
}

.statistic-card-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.statistic-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.statistic-title {
  font-size: 16px;
  color: #909399;
  margin-bottom: 10px;
}

.statistic-value {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 5px;
}

.statistic-extra {
  font-size: 13px;
  color: #909399;
}

.statistic-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #ecf5ff;
  color: #409eff;
}

.statistic-icon.warning {
  background-color: #fdf5e6;
  color: #e6a23c;
}

.statistic-icon .el-icon {
  font-size: 24px;
}

.rack-list-card {
  margin-bottom: 20px;
  width: 100%; /* Ensure card uses full width */
  max-width: 100%;
}

/* Center table content both horizontally and vertically */
.el-table {
  width: 100% !important;
  table-layout: fixed;
}

.el-table :deep(.el-table__cell) {
  text-align: center;
  vertical-align: middle;
}

/* Ensure the content within cells is also centered */
.el-table :deep(.cell) {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

/* Ensure tags are displayed properly */
.el-table :deep(.el-tag) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.rack-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rack-list-title {
  display: flex;
  flex-direction: column;
}

.rack-list-title h2 {
  margin: 0;
  font-size: 18px;
}

.subtitle {
  font-size: 13px;
  color: #909399;
}

.rack-list-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.search-input {
  width: 200px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.empty-data-container {
  padding: 40px 0;
}

.empty-action-list {
  text-align: left;
  margin: 10px auto;
  display: inline-block;
  color: #909399;
  font-size: 14px;
  line-height: 1.6;
}

.operation-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

/* Style operation buttons to look more like tags */
.operation-buttons .el-button {
  border-radius: 16px;
  padding: 5px 10px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.operation-buttons .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.operation-buttons .el-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.operation-buttons .el-icon {
  margin-right: 0;
}
</style> 