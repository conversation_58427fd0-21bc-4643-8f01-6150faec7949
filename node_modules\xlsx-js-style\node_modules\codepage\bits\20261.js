if(typeof cptable === 'undefined') cptable = {};
cptable[20261] = (function(){ var d = [], e = {}, D = [], j;
D[0] = "\u0000\u0001\u0002\u0003\u0004\u0005\u0006\u0007\b\t\n\u000b\f\r\u000e\u000f\u0010\u0011\u0012\u0013\u0014\u0015\u0016\u0017\u0018\u0019\u001a\u001b\u001c\u001d\u001e\u001f !\"��%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[]_abcdefghijklmnopqrstuvwxyz|¡¢£$¥#§¤«°±²³×µ¶·÷»¼½¾¿���������������ΩÆÐªĦĲĿŁØŒºÞŦŊŉĸæđðħıĳŀłøœßþŧŋ".split("");
for(j = 0; j != D[0].length; ++j) if(D[0][j].charCodeAt(0) !== 0xFFFD) { e[D[0][j]] = 0 + j; d[0 + j] = D[0][j];}
D[193] = "��������������������������������`��������������������������������À���È���Ì�����Ò�����Ù�Ẁ�Ỳ�������à���è���ì�����ò�����ù�ẁ�ỳ��������������������������������������������������������������������������������������������������������������������������������������".split("");
for(j = 0; j != D[193].length; ++j) if(D[193][j].charCodeAt(0) !== 0xFFFD) { e[D[193][j]] = 49408 + j; d[49408 + j] = D[193][j];}
D[194] = "��������������������������������´��������������������������������Á�Ć�É�Ǵ�Í�ḰĹḾŃÓṔ�ŔŚ�Ú�Ẃ�ÝŹ������á�ć�é�ǵ�í�ḱĺḿńóṕ�ŕś�ú�ẃ�ýź�������������������������������������������������������������������������������������������������������������������������������������".split("");
for(j = 0; j != D[194].length; ++j) if(D[194][j].charCodeAt(0) !== 0xFFFD) { e[D[194][j]] = 49664 + j; d[49664 + j] = D[194][j];}
D[195] = "��������������������������������^��������������������������������Â�Ĉ�Ê�ĜĤÎĴ����Ô���Ŝ�Û�Ŵ�ŶẐ������â�ĉ�ê�ĝĥîĵ����ô���ŝ�û�ŵ�ŷẑ�������������������������������������������������������������������������������������������������������������������������������������".split("");
for(j = 0; j != D[195].length; ++j) if(D[195][j].charCodeAt(0) !== 0xFFFD) { e[D[195][j]] = 49920 + j; d[49920 + j] = D[195][j];}
D[196] = "��������������������������������~��������������������������������Ã���Ẽ���Ĩ����ÑÕ�����ŨṼ��Ỹ�������ã���ẽ���ĩ����ñõ�����ũṽ��ỹ��������������������������������������������������������������������������������������������������������������������������������������".split("");
for(j = 0; j != D[196].length; ++j) if(D[196][j].charCodeAt(0) !== 0xFFFD) { e[D[196][j]] = 50176 + j; d[50176 + j] = D[196][j];}
D[197] = "��������������������������������¯��������������������������������Ā���Ē�Ḡ�Ī�����Ō�����Ū�����������ā���ē�ḡ�ī�����ō�����ū�����������������������������������������������������������������������������������������������������������Ǣ���������������ǣ��������������".split("");
for(j = 0; j != D[197].length; ++j) if(D[197][j].charCodeAt(0) !== 0xFFFD) { e[D[197][j]] = 50432 + j; d[50432 + j] = D[197][j];}
D[198] = "��������������������������������˘��������������������������������Ă���Ĕ�Ğ�Ĭ�����Ŏ�����Ŭ�����������ă���ĕ�ğ�ĭ�����ŏ�����ŭ������������������������������������������������������������������������������������������������������������������������������������������".split("");
for(j = 0; j != D[198].length; ++j) if(D[198][j].charCodeAt(0) !== 0xFFFD) { e[D[198][j]] = 50688 + j; d[50688 + j] = D[198][j];}
D[199] = "��������������������������������˙���������������������������������ḂĊḊĖḞĠḢİ���ṀṄ�Ṗ�ṘṠṪ��ẆẊẎŻ�������ḃċḋėḟġḣ����ṁṅ�ṗ�ṙṡṫ��ẇẋẏż�������������������������������������������������������������������������������������������������������������������������������������".split("");
for(j = 0; j != D[199].length; ++j) if(D[199][j].charCodeAt(0) !== 0xFFFD) { e[D[199][j]] = 50944 + j; d[50944 + j] = D[199][j];}
D[200] = "��������������������������������¨��������������������������������Ä���Ë��ḦÏ�����Ö�����Ü�ẄẌŸ�������ä���ë��ḧï�����ö����ẗü�ẅẍÿ��������������������������������������������������������������������������������������������������������������������������������������".split("");
for(j = 0; j != D[200].length; ++j) if(D[200][j].charCodeAt(0) !== 0xFFFD) { e[D[200][j]] = 51200 + j; d[51200 + j] = D[200][j];}
D[202] = "��������������������������������˚��������������������������������Å�������������������Ů�����������å�������������������ů�ẘ�ẙ��������������������������������������������������������������������������������������������������������������������������������������".split("");
for(j = 0; j != D[202].length; ++j) if(D[202][j].charCodeAt(0) !== 0xFFFD) { e[D[202][j]] = 51712 + j; d[51712 + j] = D[202][j];}
D[203] = "��������������������������������¸����������������������������������ÇḐ��ĢḨ��ĶĻ�Ņ���ŖŞŢ��������������çḑ��ģḩ��ķļ�ņ���ŗşţ�������������������������������������������������������������������������������������������������������������������������������������������".split("");
for(j = 0; j != D[203].length; ++j) if(D[203][j].charCodeAt(0) !== 0xFFFD) { e[D[203][j]] = 51968 + j; d[51968 + j] = D[203][j];}
D[205] = "��������������������������������˝����������������������������������������������Ő�����Ű�������������������������ő�����ű������������������������������������������������������������������������������������������������������������������������������������������".split("");
for(j = 0; j != D[205].length; ++j) if(D[205][j].charCodeAt(0) !== 0xFFFD) { e[D[205][j]] = 52480 + j; d[52480 + j] = D[205][j];}
D[206] = "��������������������������������˛��������������������������������Ą���Ę���Į�����Ǫ�����Ų�����������ą���ę���į�����ǫ�����ų������������������������������������������������������������������������������������������������������������������������������������������".split("");
for(j = 0; j != D[206].length; ++j) if(D[206][j].charCodeAt(0) !== 0xFFFD) { e[D[206][j]] = 52736 + j; d[52736 + j] = D[206][j];}
D[207] = "��������������������������������ˇ��������������������������������Ǎ�ČĎĚ�Ǧ�Ǐ�ǨĽ�ŇǑ��ŘŠŤǓ����Ž������ǎ�čďě�ǧ�ǐǰǩľ�ňǒ��řšťǔ����ž�������������������������������������������������������������������������������������������������������������������������������������".split("");
for(j = 0; j != D[207].length; ++j) if(D[207][j].charCodeAt(0) !== 0xFFFD) { e[D[207][j]] = 52992 + j; d[52992 + j] = D[207][j];}
return {"enc": e, "dec": d }; })();
