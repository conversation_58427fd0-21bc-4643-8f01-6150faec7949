<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强版机柜导出功能测试</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        h1 {
            color: #409EFF;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e4e7ed;
            border-radius: 6px;
        }
        .test-section h3 {
            color: #303133;
            margin-top: 0;
        }
        button {
            background-color: #67C23A;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        button:hover {
            background-color: #5daf34;
        }
        button:disabled {
            background-color: #c0c4cc;
            cursor: not-allowed;
        }
        .progress {
            margin: 10px 0;
            padding: 10px;
            background-color: #f0f9ff;
            border-left: 4px solid #409EFF;
            border-radius: 4px;
        }
        .success {
            background-color: #f0f9ff;
            border-left-color: #67C23A;
            color: #67C23A;
        }
        .error {
            background-color: #fef0f0;
            border-left-color: #F56C6C;
            color: #F56C6C;
        }
        .data-preview {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
        }
        .worksheet-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .worksheet-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #409EFF;
        }
        .worksheet-card h4 {
            margin: 0 0 10px 0;
            color: #303133;
        }
        .worksheet-card p {
            margin: 5px 0;
            color: #606266;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 增强版机柜导出功能测试</h1>
        
        <div class="test-section">
            <h3>📊 导出功能概览</h3>
            <div class="worksheet-info">
                <div class="worksheet-card">
                    <h4>机柜数据</h4>
                    <p>✅ 基础信息 + 环境参数</p>
                    <p>📈 14个详细字段</p>
                </div>
                <div class="worksheet-card">
                    <h4>设备数据</h4>
                    <p>🔧 设备详细信息</p>
                    <p>📍 位置、规格、状态</p>
                </div>
                <div class="worksheet-card">
                    <h4>机柜立面图</h4>
                    <p>🎨 图形化装架图</p>
                    <p>📐 ASCII艺术边框</p>
                    <p>🎯 直观设备布局</p>
                </div>
                <div class="worksheet-card">
                    <h4>机柜视图</h4>
                    <p>🏢 并排机柜显示</p>
                    <p>📊 类似图片效果</p>
                    <p>🔍 整体布局概览</p>
                </div>
                <div class="worksheet-card">
                    <h4>机房汇总</h4>
                    <p>🏢 机房级别统计</p>
                    <p>📊 容量和设备统计</p>
                </div>
                <div class="worksheet-card">
                    <h4>统计汇总</h4>
                    <p>📈 整体数据分析</p>
                    <p>⚡ 实时计算指标</p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 测试数据生成</h3>
            <p>生成模拟数据来测试导出功能的各个工作表</p>
            <button onclick="generateTestData()">生成测试数据</button>
            <button onclick="testEnhancedExport()" id="exportBtn" disabled>测试增强导出</button>
            <div id="testDataInfo"></div>
        </div>

        <div class="test-section">
            <h3>📋 导出进度</h3>
            <div id="progressLog"></div>
        </div>

        <div class="test-section">
            <h3>📄 数据预览</h3>
            <div id="dataPreview"></div>
        </div>
    </div>

    <script>
        let testRacks = [];
        let testDevices = [];
        let testDatacenters = [];

        // 生成测试数据
        function generateTestData() {
            // 生成机房数据
            testDatacenters = [
                { id: 'DC001', name: '主机房A', location: '北京数据中心1楼', description: '主要服务器机房', rackCount: 8, deviceCount: 24, created_at: '2024-01-15T08:00:00Z' },
                { id: 'DC002', name: '网络机房B', location: '北京数据中心2楼', description: '网络设备机房', rackCount: 4, deviceCount: 12, created_at: '2024-02-01T09:00:00Z' },
                { id: 'DC003', name: '存储机房C', location: '北京数据中心3楼', description: '存储设备机房', rackCount: 3, deviceCount: 9, created_at: '2024-02-15T10:00:00Z' }
            ];

            // 生成机柜数据
            testRacks = [
                { id: 'A01', name: '数据库服务器机柜', location: '主机房A-01', datacenter_id: 'DC001', total_u: 42, used_u: 8, device_count: 4, temperature: 24.5, humidity: 45, power: 2.8, max_power: 8.0, status: 'normal', description: '数据库服务器专用机柜', created_at: '2024-01-15T08:30:00Z' },
                { id: 'A02', name: 'Web服务器机柜', location: '主机房A-02', datacenter_id: 'DC001', total_u: 42, used_u: 6, device_count: 3, temperature: 23.8, humidity: 42, power: 2.1, max_power: 6.0, status: 'normal', description: 'Web应用服务器机柜', created_at: '2024-01-15T09:00:00Z' },
                { id: 'B01', name: '核心交换机柜', location: '网络机房B-01', datacenter_id: 'DC002', total_u: 42, used_u: 12, device_count: 6, temperature: 25.2, humidity: 48, power: 1.5, max_power: 4.0, status: 'warning', description: '核心网络设备机柜', created_at: '2024-02-01T09:30:00Z' },
                { id: 'C01', name: '存储阵列机柜', location: '存储机房C-01', datacenter_id: 'DC003', total_u: 42, used_u: 16, device_count: 2, temperature: 26.1, humidity: 50, power: 4.2, max_power: 10.0, status: 'normal', description: '高性能存储设备机柜', created_at: '2024-02-15T10:30:00Z' }
            ];

            // 生成设备数据
            testDevices = [
                // A01机柜设备
                { rackId: 'A01', name: 'DB-Server-01', type: 'server', model: 'Dell PowerEdge R740', position: 40, u_size: 2, power: 350, weight: 28.5, status: 'online', temperature: 45.2, serial_number: 'DL001234', ip_address: '************', description: '主数据库服务器' },
                { rackId: 'A01', name: 'DB-Server-02', type: 'server', model: 'Dell PowerEdge R740', position: 38, u_size: 2, power: 340, weight: 28.5, status: 'online', temperature: 44.8, serial_number: 'DL001235', ip_address: '************', description: '备份数据库服务器' },
                { rackId: 'A01', name: 'Storage-Switch-01', type: 'network', model: 'Cisco Nexus 3048', position: 36, u_size: 1, power: 150, weight: 8.2, status: 'online', temperature: 38.5, serial_number: 'CS001001', ip_address: '************', description: '存储网络交换机' },
                { rackId: 'A01', name: 'UPS-Monitor-01', type: 'power', model: 'APC Smart-UPS', position: 34, u_size: 3, power: 25, weight: 45.0, status: 'online', temperature: 35.2, serial_number: 'APC00101', ip_address: '************', description: 'UPS监控设备' },
                
                // A02机柜设备
                { rackId: 'A02', name: 'Web-Server-01', type: 'server', model: 'HP ProLiant DL380', position: 35, u_size: 2, power: 280, weight: 25.8, status: 'online', temperature: 42.1, serial_number: 'HP002001', ip_address: '************', description: 'Web应用服务器' },
                { rackId: 'A02', name: 'Web-Server-02', type: 'server', model: 'HP ProLiant DL380', position: 33, u_size: 2, power: 275, weight: 25.8, status: 'offline', temperature: 0, serial_number: 'HP002002', ip_address: '************', description: 'Web应用服务器(备用)' },
                { rackId: 'A02', name: 'Load-Balancer-01', type: 'network', model: 'F5 BIG-IP', position: 31, u_size: 2, power: 180, weight: 15.5, status: 'online', temperature: 40.5, serial_number: '********', ip_address: '************', description: '负载均衡器' },
                
                // B01机柜设备
                { rackId: 'B01', name: 'Core-Switch-01', type: 'network', model: 'Cisco Catalyst 9500', position: 30, u_size: 4, power: 400, weight: 18.2, status: 'online', temperature: 48.5, serial_number: 'CC004001', ip_address: '*************', description: '核心交换机' },
                { rackId: 'B01', name: 'Core-Switch-02', type: 'network', model: 'Cisco Catalyst 9500', position: 26, u_size: 4, power: 395, weight: 18.2, status: 'online', temperature: 47.8, serial_number: 'CC004002', ip_address: '*************', description: '核心交换机(备用)' },
                { rackId: 'B01', name: 'Firewall-01', type: 'security', model: 'Fortinet FortiGate', position: 22, u_size: 2, power: 120, weight: 8.5, status: 'online', temperature: 45.2, serial_number: 'FG005001', ip_address: '**************', description: '防火墙设备' },
                { rackId: 'B01', name: 'Router-01', type: 'network', model: 'Cisco ISR 4431', position: 20, u_size: 2, power: 200, weight: 12.8, status: 'online', temperature: 46.1, serial_number: 'CR006001', ip_address: '**************', description: '边界路由器' },
                
                // C01机柜设备
                { rackId: 'C01', name: 'Storage-Array-01', type: 'storage', model: 'NetApp FAS8200', position: 25, u_size: 8, power: 800, weight: 85.5, status: 'online', temperature: 52.1, serial_number: 'NA007001', ip_address: '**************', description: '主存储阵列' },
                { rackId: 'C01', name: 'Backup-Storage-01', type: 'storage', model: 'Dell EMC Unity', position: 17, u_size: 8, power: 750, weight: 78.2, status: 'online', temperature: 51.8, serial_number: 'DE008001', ip_address: '**************', description: '备份存储阵列' }
            ];

            // 更新界面
            document.getElementById('testDataInfo').innerHTML = `
                <div class="progress success">
                    ✅ 测试数据生成完成！<br>
                    📊 机房: ${testDatacenters.length} 个<br>
                    🏢 机柜: ${testRacks.length} 个<br>
                    💻 设备: ${testDevices.length} 台
                </div>
            `;
            
            document.getElementById('exportBtn').disabled = false;
            
            // 显示数据预览
            showDataPreview();
        }

        // 显示数据预览
        function showDataPreview() {
            const preview = document.getElementById('dataPreview');
            preview.innerHTML = `
                <h4>🏢 机柜数据预览</h4>
                <div class="data-preview">
${testRacks.map(rack => `机柜: ${rack.id} | ${rack.name} | ${rack.device_count}台设备 | ${rack.temperature}°C`).join('\n')}
                </div>
                
                <h4>💻 设备数据预览</h4>
                <div class="data-preview">
${testDevices.slice(0, 5).map(device => `${device.rackId}: ${device.name} | ${device.type} | U${device.position} | ${device.status}`).join('\n')}
... 共${testDevices.length}台设备
                </div>
            `;
        }

        // 模拟增强导出功能
        async function testEnhancedExport() {
            const progressLog = document.getElementById('progressLog');
            const exportBtn = document.getElementById('exportBtn');
            
            exportBtn.disabled = true;
            progressLog.innerHTML = '';
            
            try {
                // 步骤1: 准备机柜数据
                addProgress('正在准备机柜基础数据...', 'progress');
                await sleep(500);
                
                // 步骤2: 获取设备数据
                addProgress('正在获取设备详细数据...', 'progress');
                for (let i = 0; i < testRacks.length; i++) {
                    addProgress(`正在处理机柜 ${testRacks[i].id} (${i + 1}/${testRacks.length})...`, 'progress');
                    await sleep(200);
                }
                
                // 步骤3-7: 生成各个工作表
                const steps = [
                    '正在生成设备数据工作表...',
                    '正在生成机柜立面图...',
                    '正在获取机房数据...',
                    '正在生成统计汇总...',
                    '正在生成Excel文件...'
                ];
                
                for (const step of steps) {
                    addProgress(step, 'progress');
                    await sleep(300);
                }
                
                // 生成实际的Excel文件
                generateExcelFile();
                
                addProgress(`✅ 成功导出 ${testRacks.length} 个机柜、${testDevices.length} 台设备的完整数据，包含 6 个工作表（含机柜视图）`, 'success');
                
            } catch (error) {
                addProgress(`❌ 导出失败: ${error.message}`, 'error');
            } finally {
                exportBtn.disabled = false;
            }
        }

        // 生成实际的Excel文件
        function generateExcelFile() {
            const wb = XLSX.utils.book_new();
            
            // 工作表1: 机柜数据
            const rackData = [
                ['机柜编号', '机柜名称', '机房位置', '机房ID', '总容量(U)', '已使用(U)', '设备数量', '温度(°C)', '湿度(%)', '当前功率(kW)', '最大功率(kW)', '状态', '描述', '创建时间']
            ];
            testRacks.forEach(rack => {
                rackData.push([
                    rack.id, rack.name, rack.location, rack.datacenter_id, rack.total_u, rack.used_u,
                    rack.device_count, rack.temperature, rack.humidity, rack.power, rack.max_power,
                    rack.status === 'normal' ? '正常' : '告警', rack.description,
                    new Date(rack.created_at).toLocaleString()
                ]);
            });
            const wsRacks = XLSX.utils.aoa_to_sheet(rackData);
            XLSX.utils.book_append_sheet(wb, wsRacks, '机柜数据');
            
            // 工作表2: 设备数据
            const deviceData = [
                ['机柜编号', '设备名称', '设备类型', '设备型号', 'U位置', 'U高度', '功率(W)', '重量(kg)', '状态', '温度(°C)', '序列号', 'IP地址', '描述']
            ];
            testDevices.forEach(device => {
                deviceData.push([
                    device.rackId, device.name, device.type, device.model, device.position, device.u_size,
                    device.power, device.weight, device.status === 'online' ? '在线' : '离线',
                    device.temperature, device.serial_number, device.ip_address, device.description
                ]);
            });
            const wsDevices = XLSX.utils.aoa_to_sheet(deviceData);
            XLSX.utils.book_append_sheet(wb, wsDevices, '设备数据');
            
            // 工作表3: 机柜立面图（简化版）
            const elevationData = [['机柜编号', 'U位', '设备名称', '设备类型', '备注']];
            testRacks.forEach(rack => {
                const rackDevices = testDevices.filter(d => d.rackId === rack.id);
                for (let u = 42; u >= 1; u--) {
                    const device = rackDevices.find(d => d.position <= u && d.position + d.u_size - 1 >= u);
                    elevationData.push([
                        rack.id,
                        `U${u}`,
                        device ? device.name : '空闲',
                        device ? device.type : '',
                        device && device.position === u ? `${device.u_size}U设备` : (device ? '↑' : '')
                    ]);
                }
                elevationData.push(['', '', '', '', '']); // 分隔行
            });
            const wsElevation = XLSX.utils.aoa_to_sheet(elevationData);
            XLSX.utils.book_append_sheet(wb, wsElevation, '机柜立面图');

            // 工作表4: 机柜视图（并排显示机柜）
            const testDevicesMap = {};
            testRacks.forEach(rack => {
                testDevicesMap[rack.id] = testDevices.filter(d => d.rackId === rack.id);
            });

            const rackViewData = generateRackView(testRacks, testDevicesMap);
            const wsRackView = XLSX.utils.aoa_to_sheet(rackViewData);

            // 设置列宽以适应表格形式机柜视图
            const colWidths = [];
            const maxCols = Math.max(...rackViewData.map(row => row.length));
            for (let i = 0; i < maxCols; i++) {
                const colIndex = i % 4; // 每个机柜占用4列：左U位、设备内容、右U位、分隔
                if (colIndex === 0 || colIndex === 2) {
                    colWidths.push({ wch: 3 }); // U位列，较窄
                } else if (colIndex === 1) {
                    colWidths.push({ wch: 25 }); // 设备内容列，较宽
                } else {
                    colWidths.push({ wch: 2 }); // 分隔列，最窄
                }
            }
            wsRackView['!cols'] = colWidths;

            // 设置行高
            const rowHeights = [];
            for (let i = 0; i < rackViewData.length; i++) {
                rowHeights[i] = { hpt: 16 }; // 设置行高为16磅，稍微高一点
            }
            wsRackView['!rows'] = rowHeights;

            // 为每个机柜添加独立的边框
            const range = XLSX.utils.decode_range(wsRackView['!ref']);
            const colsPerRack = 4; // 每个机柜占用4列：左U位、设备内容、右U位、分隔

            for (let R = range.s.r; R <= range.e.r; ++R) {
                for (let C = range.s.c; C <= range.e.c; ++C) {
                    const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
                    if (!wsRackView[cellAddress]) {
                        wsRackView[cellAddress] = { t: 's', v: '' };
                    }

                    // 添加边框样式
                    if (!wsRackView[cellAddress].s) {
                        wsRackView[cellAddress].s = {};
                    }

                    const colIndex = C % colsPerRack;

                    // 只为机柜相关列添加边框（不包括分隔列）
                    if (colIndex !== 3) {
                        // 确定边框样式
                        const borderStyle = { style: 'thin', color: { rgb: '000000' } };
                        const thickBorderStyle = { style: 'medium', color: { rgb: '000000' } };

                        wsRackView[cellAddress].s.border = {
                            top: borderStyle,
                            bottom: borderStyle,
                            left: colIndex === 0 ? thickBorderStyle : borderStyle,  // 机柜左边界用粗线
                            right: colIndex === 2 ? thickBorderStyle : borderStyle   // 机柜右边界用粗线
                        };

                        // 为机柜标题行和第一个U位行添加粗上边框
                        if (R === range.s.r || (R > range.s.r && rackViewData[R-1] && rackViewData[R-1].every(cell => cell === ''))) {
                            wsRackView[cellAddress].s.border.top = thickBorderStyle;
                        }

                        // 为最后一个U位行添加粗下边框
                        if (R === range.e.r || (R < range.e.r && rackViewData[R+1] && rackViewData[R+1].every(cell => cell === ''))) {
                            wsRackView[cellAddress].s.border.bottom = thickBorderStyle;
                        }

                        // 设置文本居中对齐
                        wsRackView[cellAddress].s.alignment = {
                            horizontal: 'center',
                            vertical: 'center'
                        };
                    }
                }
            }

            XLSX.utils.book_append_sheet(wb, wsRackView, '机柜视图');

            // 工作表5: 机房汇总
            const datacenterData = [
                ['机房ID', '机房名称', '机房位置', '机房描述', '机柜数量', '设备数量', '创建时间']
            ];
            testDatacenters.forEach(dc => {
                datacenterData.push([
                    dc.id, dc.name, dc.location, dc.description, dc.rackCount, dc.deviceCount,
                    new Date(dc.created_at).toLocaleString()
                ]);
            });
            const wsDatacenters = XLSX.utils.aoa_to_sheet(datacenterData);
            XLSX.utils.book_append_sheet(wb, wsDatacenters, '机房汇总');
            
            // 工作表6: 统计汇总
            const onlineDevices = testDevices.filter(d => d.status === 'online').length;
            const offlineDevices = testDevices.filter(d => d.status === 'offline').length;
            const totalU = testRacks.reduce((sum, rack) => sum + rack.total_u, 0);
            const usedU = testRacks.reduce((sum, rack) => sum + rack.used_u, 0);
            const avgTemp = (testRacks.reduce((sum, rack) => sum + rack.temperature, 0) / testRacks.length).toFixed(1);
            const totalPower = testRacks.reduce((sum, rack) => sum + rack.power, 0).toFixed(2);
            
            const statsData = [
                ['统计项目', '数值', '单位'],
                ['机柜总数', testRacks.length, '个'],
                ['设备总数', testDevices.length, '台'],
                ['在线设备', onlineDevices, '台'],
                ['离线设备', offlineDevices, '台'],
                ['总U位容量', totalU, 'U'],
                ['已使用U位', usedU, 'U'],
                ['平均温度', avgTemp, '°C'],
                ['总功率', totalPower, 'kW'],
                ['导出时间', new Date().toLocaleString(), '']
            ];
            const wsStats = XLSX.utils.aoa_to_sheet(statsData);
            XLSX.utils.book_append_sheet(wb, wsStats, '统计汇总');
            
            // 下载文件
            const now = new Date();
            const dateStr = now.getFullYear() + '-' + 
                           String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                           String(now.getDate()).padStart(2, '0');
            const fileName = `机柜管理数据_${dateStr}_测试版.xlsx`;
            
            XLSX.writeFile(wb, fileName);
        }

        // 添加进度日志
        function addProgress(message, type = 'progress') {
            const progressLog = document.getElementById('progressLog');
            const div = document.createElement('div');
            div.className = `progress ${type}`;
            div.textContent = message;
            progressLog.appendChild(div);
            progressLog.scrollTop = progressLog.scrollHeight;
        }

        // 生成机柜视图数据（完全按照用户图片样式的表格形式机柜图）
        function generateRackView(racks, allDevicesMap) {
            const rackView = [];
            const racksPerRow = 3; // 每行显示3个机柜，类似图片效果

            // 将机柜分组，每组包含racksPerRow个机柜
            const rackRows = [];
            for (let i = 0; i < racks.length; i += racksPerRow) {
                rackRows.push(racks.slice(i, i + racksPerRow));
            }

            // 为每组机柜生成视图
            rackRows.forEach((rackGroup, groupIndex) => {
                // 为每个机柜创建U位占用映射
                const rackMaps = rackGroup.map(rack => {
                    const devices = allDevicesMap[rack.id] || [];
                    const uPositions = new Array(47).fill(null); // 1-based indexing, 支持到U46

                    devices.forEach(device => {
                        const startPos = device.position;
                        const endPos = startPos + (device.u_size || 1) - 1;

                        for (let u = startPos; u <= endPos && u <= 46; u++) {
                            uPositions[u] = {
                                deviceName: device.name,
                                deviceType: device.type || '未知',
                                isStart: u === startPos,
                                isEnd: u === endPos,
                                uSize: device.u_size || 1,
                                status: device.status || 'unknown'
                            };
                        }
                    });

                    return { rack, uPositions };
                });

                // 添加机柜标题行
                const titleRow = [];
                rackMaps.forEach((rackMap, index) => {
                    const rack = rackMap.rack;
                    // 左U位列
                    titleRow.push('');
                    // 机柜标题列
                    titleRow.push(`${rack.name}-${rack.max_power || '5000'}W`);
                    // 右U位列
                    titleRow.push('');

                    if (index < rackMaps.length - 1) {
                        titleRow.push(''); // 机柜间隔列
                    }
                });
                rackView.push(titleRow);

                // 生成U位行（从U46到U1）
                for (let u = 46; u >= 1; u--) {
                    const row = [];

                    rackMaps.forEach((rackMap, rackIndex) => {
                        const { uPositions } = rackMap;
                        const uLabel = u.toString();

                        // 左侧U位标号
                        row.push(uLabel);

                        // 中间设备内容
                        const device = uPositions[u];
                        let deviceContent = '';

                        if (device) {
                            if (device.isStart) {
                                // 设备起始位置，显示设备名称
                                deviceContent = device.deviceName;
                            } else {
                                // 设备延续位置，显示设备名称（保持一致）
                                deviceContent = device.deviceName;
                            }
                        } else {
                            // 空闲位置
                            deviceContent = '';
                        }

                        row.push(deviceContent);

                        // 右侧U位标号
                        row.push(uLabel);

                        // 在机柜之间添加分隔列
                        if (rackIndex < rackMaps.length - 1) {
                            row.push(''); // 机柜间隔列
                        }
                    });

                    rackView.push(row);
                }

                // 在机柜组之间添加分隔行
                if (groupIndex < rackRows.length - 1) {
                    rackView.push(new Array(titleRow.length).fill(''));
                    rackView.push(new Array(titleRow.length).fill(''));
                }
            });

            return rackView;
        }

        // 延时函数
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addProgress('🎉 增强版机柜导出功能测试页面已加载', 'success');
            addProgress('👆 请先点击"生成测试数据"按钮创建模拟数据', 'progress');
        });
    </script>
</body>
</html>
