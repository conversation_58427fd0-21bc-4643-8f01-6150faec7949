{"name": "commander", "version": "2.17.1", "description": "the complete solution for node.js command-line programs", "keywords": ["commander", "command", "option", "parser"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tj/commander.js.git"}, "scripts": {"lint": "eslint index.js", "test": "node test/run.js && npm run test-typings", "test-typings": "tsc -p tsconfig.json"}, "main": "index", "files": ["index.js", "typings/index.d.ts"], "dependencies": {}, "devDependencies": {"@types/node": "^10.5.7", "eslint": "^5.3.0", "should": "^13.2.3", "sinon": "^6.1.4", "standard": "^11.0.1", "typescript": "^2.9.2"}, "typings": "typings/index.d.ts"}